{"name": "frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:watch": "ng test --watch", "test:ci": "ng test --watch=false --browsers=ChromeHeadless --code-coverage", "test:coverage": "ng test --code-coverage --watch=false --browsers=ChromeHeadless", "test:coverage-open": "ng test --code-coverage --watch=false --browsers=ChromeHeadless && start coverage/planify/index.html", "test:single-run": "ng test --watch=false --browsers=ChromeHeadless", "coverage:check": "nyc check-coverage --statements 80 --branches 60 --functions 75 --lines 80"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "karma-junit-reporter": "~2.0.1", "typescript": "~5.7.2"}}