# Manuel de Mise à Jour - Planify

## Vue d'ensemble

Ce manuel détaille les procédures de mise à jour des composants du projet Planify, incluant les dépendances frontend et backend, les images Docker de base, et la gestion du versioning.

## Gestion du versioning

### Fichier de version principal

Le versioning est centralisé dans `planify.properties` :
```properties
version.prefix=25.8.1
```

### Scripts de versioning

Les scripts dans `build-scripts/` gèrent automatiquement :
- `create-versions-files.sh` : Génération des fichiers de version
- `set-version-suffix.sh` : Gestion des suffixes de version
- `replace-versions-in-template-files.sh` : Remplacement dans les templates

### Mise à jour de version

```bash
# Modification de la version
echo "version.prefix=25.8.2" > planify.properties

# Génération des fichiers de version
./build-scripts/create-versions-files.sh

# Commit et push pour déclencher le pipeline
git add planify.properties
git commit -m "Bump version to 25.8.2"
git push
```

## Mise à jour des dépendances Frontend

### Dépendances Angular actuelles

Versions dans `frontend/package.json` :
```json
{
  "dependencies": {
    "@angular/common": "^19.2.0",
    "@angular/compiler": "^19.2.0",
    "@angular/core": "^19.2.0",
    "@angular/forms": "^19.2.0",
    "@angular/platform-browser": "^19.2.0",
    "@angular/platform-browser-dynamic": "^19.2.0",
    "rxjs": "~7.8.0",
    "tslib": "^2.3.0",
    "zone.js": "~0.15.0"
  },
  "devDependencies": {
    "@angular-devkit/build-angular": "^19.2.13",
    "@angular/cli": "^19.2.13",
    "@angular/compiler-cli": "^19.2.0",
    "@types/jasmine": "~5.1.0",
    "jasmine-core": "~5.6.0",
    "karma": "~6.4.0",
    "karma-chrome-launcher": "~3.2.0",
    "karma-coverage": "~2.2.0",
    "karma-jasmine": "~5.1.0",
    "karma-jasmine-html-reporter": "~2.1.0",
    "typescript": "~5.7.2"
  }
}
```

### Procédure de mise à jour Angular

#### 1. Vérification des versions disponibles
```bash
cd frontend
npm outdated
ng update
```

#### 2. Mise à jour Angular CLI
```bash
npm install -g @angular/cli@latest
ng version
```

#### 3. Mise à jour du framework Angular
```bash
# Mise à jour majeure (ex: 19 -> 20)
ng update @angular/core @angular/cli

# Mise à jour mineure/patch
ng update @angular/core @angular/cli --force
```

#### 4. Mise à jour des dépendances individuelles
```bash
# TypeScript
ng update typescript

# RxJS
ng update rxjs

# Outils de test
ng update @angular-devkit/build-angular
```

### Configuration TypeScript

Fichier `frontend/tsconfig.json` - Vérifier la compatibilité :
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true
  }
}
```

### Tests après mise à jour Frontend

```bash
cd frontend

# Installation des dépendances
npm install

# Vérification de la compilation
ng build

# Exécution des tests
npm test

# Tests de couverture
npm run test:coverage

# Vérification des seuils de couverture
npm run coverage:check
```

## Mise à jour des dépendances Backend

### Dépendances Python actuelles

Fichier `backend/requirements.txt` :
```
Flask
Flask-CORS
requests
python-dotenv
gunicorn
```

### Procédure de mise à jour Python

#### 1. Vérification des versions actuelles
```bash
cd backend
pip list --outdated
pip show Flask Flask-CORS requests python-dotenv gunicorn
```

#### 2. Mise à jour avec versions spécifiques
```bash
# Création d'un environnement de test
python -m venv venv-test
source venv-test/bin/activate  # Linux/Mac
# ou
venv-test\Scripts\activate     # Windows

# Installation des versions actuelles
pip install -r requirements.txt

# Mise à jour individuelle avec test
pip install Flask==3.0.0
pip install Flask-CORS==4.0.0
pip install requests==2.31.0
pip install python-dotenv==1.0.0
pip install gunicorn==21.2.0
```

#### 3. Génération du nouveau requirements.txt
```bash
# Avec versions exactes
pip freeze > requirements-new.txt

# Ou avec versions flexibles (recommandé)
cat > requirements.txt << EOF
Flask>=3.0.0,<4.0.0
Flask-CORS>=4.0.0,<5.0.0
requests>=2.31.0,<3.0.0
python-dotenv>=1.0.0,<2.0.0
gunicorn>=21.2.0,<22.0.0
EOF
```

### Tests après mise à jour Backend

```bash
cd backend

# Installation des nouvelles dépendances
pip install -r requirements.txt

# Test de démarrage
python src/app.py

# Test des endpoints
curl http://localhost:5000/api/health
curl http://localhost:5000/api/sprints

# Tests unitaires (si disponibles)
python -m pytest tests/
```

## Mise à jour des images Docker de base

### Images actuelles

#### Frontend (nginx)
Fichier `frontend/docker/Dockerfile` :
```dockerfile
ARG IMAGE_VERSION=25.6.4
FROM itesoft/external/nginx1:${IMAGE_VERSION}
```

#### Backend (Python)
Fichier `backend/docker/Dockerfile` :
```dockerfile
ARG IMAGE_VERSION=25.7.1
FROM itesoft/external/python3_11:${IMAGE_VERSION}
```

### Procédure de mise à jour des images

#### 1. Vérification des nouvelles versions
```bash
# Consultation du registre Docker
docker search itesoft/external/nginx1
docker search itesoft/external/python3_11

# Ou consultation de la documentation interne
```

#### 2. Mise à jour de l'image Nginx
```bash
# Modification du Dockerfile frontend
sed -i 's/ARG IMAGE_VERSION=25.6.4/ARG IMAGE_VERSION=25.7.0/' frontend/docker/Dockerfile

# Test de build
cd frontend
docker build -f docker/Dockerfile -t planify-frontend-test .
```

#### 3. Mise à jour de l'image Python
```bash
# Modification du Dockerfile backend
sed -i 's/ARG IMAGE_VERSION=25.7.1/ARG IMAGE_VERSION=25.8.0/' backend/docker/Dockerfile

# Test de build
cd backend
docker build -f docker/Dockerfile -t planify-backend-test .
```

### Tests des nouvelles images

```bash
# Test de l'image frontend
docker run -d -p 8080:80 --name test-frontend planify-frontend-test
curl http://localhost:8080

# Test de l'image backend
docker run -d -p 8000:5000 --name test-backend planify-backend-test
curl http://localhost:8000/api/health

# Nettoyage
docker stop test-frontend test-backend
docker rm test-frontend test-backend
docker rmi planify-frontend-test planify-backend-test
```

## Mise à jour des outils de build

### Images CI/CD

Fichier `.gitlab-ci.yml` - Images utilisées :
```yaml
.default-node-builder:
  image:
    name: itesoft/tool/node20-builder:24.10.1

.default-docker-builder:
  image:
    name: itesoft/tool/docker-builder:25.6.1
```

### Mise à jour des images CI
```bash
# Modification des versions dans .gitlab-ci.yml
sed -i 's/node20-builder:24.10.1/node20-builder:24.11.0/' .gitlab-ci.yml
sed -i 's/docker-builder:25.6.1/docker-builder:25.7.0/' .gitlab-ci.yml
```

## Tests de régression après mise à jour

### Tests automatisés Frontend

```bash
cd frontend

# Tests unitaires complets
npm test

# Tests avec couverture
npm run test:coverage

# Vérification des seuils (80% statements, 75% branches)
npm run coverage:check

# Build de production
npm run build

# Vérification de la taille des bundles
ls -la dist/frontend/browser/
```

### Tests fonctionnels

#### 1. Tests d'interface
- Chargement de la page d'accueil
- Sélection d'un sprint
- Affichage de la timeline
- Fonctionnement des tooltips
- Scroll horizontal et vertical
- Boutons développer/réduire

#### 2. Tests d'intégration
```bash
# Démarrage complet avec Docker Compose
docker-compose up -d

# Tests des endpoints
curl http://localhost:5000/api/health
curl http://localhost:5000/api/sprints
curl "http://localhost:5000/api/sprints/MyProject%5CSprint%201/features"

# Test de l'interface
curl http://localhost:4200/
```

### Tests de performance

```bash
# Mesure des temps de build
time npm run build

# Taille des images Docker
docker images | grep planify

# Temps de démarrage des conteneurs
time docker-compose up -d
```

## Procédure de rollback

### Rollback des dépendances

#### Frontend
```bash
cd frontend
git checkout HEAD~1 -- package.json package-lock.json
npm install
npm run build
```

#### Backend
```bash
cd backend
git checkout HEAD~1 -- requirements.txt
pip install -r requirements.txt
```

### Rollback des images Docker

```bash
# Retour aux versions précédentes
git checkout HEAD~1 -- frontend/docker/Dockerfile
git checkout HEAD~1 -- backend/docker/Dockerfile

# Rebuild des images
docker-compose build
docker-compose up -d
```

### Rollback de version

```bash
# Retour à la version précédente
git checkout HEAD~1 -- planify.properties

# Régénération des fichiers de version
./build-scripts/create-versions-files.sh
```

## Checklist de mise à jour

### Avant la mise à jour
- [ ] Sauvegarde de la configuration actuelle
- [ ] Documentation des versions actuelles
- [ ] Tests de l'environnement actuel
- [ ] Planification de la fenêtre de maintenance

### Pendant la mise à jour
- [ ] Mise à jour du versioning (`planify.properties`)
- [ ] Mise à jour des dépendances frontend
- [ ] Mise à jour des dépendances backend
- [ ] Mise à jour des images Docker de base
- [ ] Tests de build et compilation

### Après la mise à jour
- [ ] Tests de régression complets
- [ ] Vérification des performances
- [ ] Tests d'intégration Azure DevOps
- [ ] Validation en environnement de test
- [ ] Documentation des changements
- [ ] Déploiement en production

### En cas de problème
- [ ] Procédure de rollback testée
- [ ] Logs et diagnostics collectés
- [ ] Équipe de support notifiée
- [ ] Plan de correction établi

## Bonnes pratiques

### Gestion des versions
- Toujours tester en environnement isolé
- Documenter les changements breaking
- Maintenir la compatibilité ascendante
- Utiliser le versioning sémantique

### Tests
- Automatiser les tests de régression
- Maintenir une couverture de code élevée
- Tester sur plusieurs environnements
- Valider les performances

### Déploiement
- Déploiements progressifs (blue-green)
- Monitoring post-déploiement
- Rollback automatique en cas d'erreur
- Communication avec les utilisateurs
