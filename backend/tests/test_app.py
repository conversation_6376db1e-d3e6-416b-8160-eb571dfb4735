"""
Tests unitaires pour l'API Planify Backend
"""
import pytest
import json
from unittest.mock import patch, MagicMock
import sys
import os

# Ajouter le répertoire src au path pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from app import app


@pytest.fixture
def client():
    """Fixture pour le client de test Flask"""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client


@pytest.fixture
def mock_env_vars():
    """Fixture pour mocker les variables d'environnement"""
    with patch.dict(os.environ, {
        'AZURE_DEVOPS_SERVER_URL': 'https://test.azure.com',
        'AZURE_DEVOPS_ORG': 'TestOrg',
        'AZURE_DEVOPS_PROJECT': 'TestProject',
        'AZURE_DEVOPS_TEAM': 'TestTeam',
        'AZURE_DEVOPS_PAT': '',  # Mode test
        'SPRINT_PATH_PREFIX': 'TestProject\\Sprint'
    }):
        yield


class TestHealthEndpoint:
    """Tests pour l'endpoint de health check"""
    
    def test_health_check_success(self, client):
        """Test du health check - doit retourner status healthy"""
        response = client.get('/api/health')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'timestamp' in data
    
    def test_health_check_content_type(self, client):
        """Test du content-type de la réponse health check"""
        response = client.get('/api/health')
        
        assert response.content_type == 'application/json'


class TestSprintsEndpoint:
    """Tests pour l'endpoint des sprints"""
    
    def test_get_sprints_test_mode(self, client, mock_env_vars):
        """Test récupération des sprints en mode test"""
        response = client.get('/api/sprints')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Vérifier la structure d'un sprint
        sprint = data[0]
        assert 'id' in sprint
        assert 'name' in sprint
        assert 'path' in sprint
        assert 'attributes' in sprint
        assert 'url' in sprint
    
    def test_get_sprints_structure(self, client, mock_env_vars):
        """Test de la structure des données de sprint"""
        response = client.get('/api/sprints')
        data = json.loads(response.data)
        
        for sprint in data:
            # Vérifier les attributs requis
            assert isinstance(sprint['id'], str)
            assert isinstance(sprint['name'], str)
            assert isinstance(sprint['path'], str)
            assert isinstance(sprint['attributes'], dict)
            
            # Vérifier les attributs du sprint
            attributes = sprint['attributes']
            assert 'startDate' in attributes
            assert 'finishDate' in attributes
            assert 'timeFrame' in attributes


class TestSprintFeaturesEndpoint:
    """Tests pour l'endpoint des features d'un sprint"""
    
    def test_get_sprint_features_test_mode(self, client, mock_env_vars):
        """Test récupération des features d'un sprint en mode test"""
        sprint_path = 'MyProject\\Sprint 1'
        response = client.get(f'/api/sprints/{sprint_path}/features')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(data, list)
    
    def test_get_sprint_features_structure(self, client, mock_env_vars):
        """Test de la structure des work items"""
        sprint_path = 'MyProject\\Sprint 1'
        response = client.get(f'/api/sprints/{sprint_path}/features')
        data = json.loads(response.data)
        
        for work_item in data:
            assert 'id' in work_item
            assert 'fields' in work_item
            assert 'url' in work_item
            
            fields = work_item['fields']
            assert 'System.Id' in fields
            assert 'System.Title' in fields
            assert 'System.WorkItemType' in fields
            assert 'System.State' in fields
    
    def test_get_sprint_features_encoded_path(self, client, mock_env_vars):
        """Test avec un chemin de sprint encodé URL"""
        sprint_path = 'MyProject%5CSprint%201'  # Encodé
        response = client.get(f'/api/sprints/{sprint_path}/features')
        
        assert response.status_code == 200
    
    def test_get_sprint_features_nonexistent(self, client, mock_env_vars):
        """Test avec un sprint inexistant"""
        sprint_path = 'NonExistent\\Sprint'
        response = client.get(f'/api/sprints/{sprint_path}/features')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(data, list)
        assert len(data) == 0  # Aucun work item pour ce sprint


class TestErrorHandling:
    """Tests pour la gestion d'erreurs"""
    
    def test_404_endpoint(self, client):
        """Test d'un endpoint inexistant"""
        response = client.get('/api/nonexistent')
        
        assert response.status_code == 404
    
    def test_invalid_method(self, client):
        """Test d'une méthode HTTP non supportée"""
        response = client.post('/api/health')
        
        assert response.status_code == 405


class TestCORS:
    """Tests pour la configuration CORS"""
    
    def test_cors_headers(self, client):
        """Test de la présence des headers CORS"""
        response = client.get('/api/health')
        
        # Vérifier que les headers CORS sont présents
        assert 'Access-Control-Allow-Origin' in response.headers
    
    def test_options_request(self, client):
        """Test d'une requête OPTIONS pour CORS preflight"""
        response = client.options('/api/health')
        
        # La requête OPTIONS doit être gérée
        assert response.status_code in [200, 204]


class TestDataValidation:
    """Tests pour la validation des données"""
    
    def test_sprint_data_types(self, client, mock_env_vars):
        """Test des types de données dans les sprints"""
        response = client.get('/api/sprints')
        data = json.loads(response.data)
        
        for sprint in data:
            # Vérifier que les dates sont des strings ISO
            start_date = sprint['attributes']['startDate']
            finish_date = sprint['attributes']['finishDate']
            
            assert isinstance(start_date, str)
            assert isinstance(finish_date, str)
            assert 'T' in start_date or '-' in start_date  # Format ISO
            assert 'T' in finish_date or '-' in finish_date
    
    def test_work_item_data_types(self, client, mock_env_vars):
        """Test des types de données dans les work items"""
        sprint_path = 'MyProject\\Sprint 1'
        response = client.get(f'/api/sprints/{sprint_path}/features')
        data = json.loads(response.data)
        
        for work_item in data:
            assert isinstance(work_item['id'], int)
            assert isinstance(work_item['fields']['System.Id'], int)
            assert isinstance(work_item['fields']['System.Title'], str)
            assert isinstance(work_item['fields']['System.WorkItemType'], str)


if __name__ == '__main__':
    pytest.main([__file__])
