import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';
import { UserStoryTooltipDirective } from './user-story-tooltip.directive';
import { WorkItem } from '../interfaces/work-item';

@Component({
  template: `
    <div
      [appUserStoryTooltip]="userStory"
      class="test-element"
      style="width: 100px; height: 50px;">
      Test Element
    </div>
  `,
  standalone: true,
  imports: [UserStoryTooltipDirective]
})
class TestComponent {
  userStory: WorkItem | null = {
    id: 123,
    rev: 1,
    fields: {
      'System.Id': 123,
      'System.Title': 'Test User Story',
      'System.State': 'Active',
      'System.WorkItemType': 'User Story',
      'System.CreatedDate': '2024-01-01T00:00:00Z',
      'System.AssignedTo': {
        displayName: '<PERSON>',
        uniqueName: '<EMAIL>'
      },
      'Microsoft.VSTS.Scheduling.StartDate': '2024-01-15T00:00:00Z',
      'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-20T00:00:00Z'
    },
    url: 'test-url'
  };
}

describe('UserStoryTooltipDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let directiveElement: DebugElement;
  let directive: UserStoryTooltipDirective;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    directiveElement = fixture.debugElement.query(By.directive(UserStoryTooltipDirective));
    directive = directiveElement.injector.get(UserStoryTooltipDirective);
    fixture.detectChanges();
  });

  it('should create directive', () => {
    expect(directive).toBeTruthy();
  });

  it('should have userStory input bound', () => {
    expect(directive.userStory).toEqual(component.userStory);
  });

  it('should show tooltip on mouseenter after delay', (done) => {
    const element = directiveElement.nativeElement;

    // Simuler mouseenter
    element.dispatchEvent(new MouseEvent('mouseenter'));

    // Vérifier qu'aucun tooltip n'est immédiatement visible
    expect(document.querySelector('app-user-story-tooltip')).toBeNull();

    // Attendre le délai de 300ms
    setTimeout(() => {
      const tooltip = document.querySelector('app-user-story-tooltip');
      expect(tooltip).toBeTruthy();
      done();
    }, 350);
  });

  it('should hide tooltip on mouseleave after delay', (done) => {
    const element = directiveElement.nativeElement;

    // Afficher le tooltip d'abord
    element.dispatchEvent(new MouseEvent('mouseenter'));

    setTimeout(() => {
      // Vérifier que le tooltip est visible
      expect(document.querySelector('app-user-story-tooltip')).toBeTruthy();

      // Simuler mouseleave
      element.dispatchEvent(new MouseEvent('mouseleave'));

      // Attendre le délai de masquage
      setTimeout(() => {
        expect(document.querySelector('app-user-story-tooltip')).toBeNull();
        done();
      }, 150);
    }, 350);
  });

  it('should cancel show timeout on mouseleave before tooltip appears', (done) => {
    const element = directiveElement.nativeElement;

    // Simuler mouseenter puis mouseleave rapidement
    element.dispatchEvent(new MouseEvent('mouseenter'));
    element.dispatchEvent(new MouseEvent('mouseleave'));

    // Attendre plus que le délai d'affichage
    setTimeout(() => {
      expect(document.querySelector('app-user-story-tooltip')).toBeNull();
      done();
    }, 350);
  });

  it('should cancel hide timeout on mouseenter while tooltip is visible', (done) => {
    const element = directiveElement.nativeElement;

    // Afficher le tooltip
    element.dispatchEvent(new MouseEvent('mouseenter'));

    setTimeout(() => {
      // Vérifier que le tooltip est visible
      expect(document.querySelector('app-user-story-tooltip')).toBeTruthy();

      // Simuler mouseleave puis mouseenter rapidement
      element.dispatchEvent(new MouseEvent('mouseleave'));
      element.dispatchEvent(new MouseEvent('mouseenter'));

      // Attendre plus que le délai de masquage
      setTimeout(() => {
        // Le tooltip devrait toujours être visible
        expect(document.querySelector('app-user-story-tooltip')).toBeTruthy();
        done();
      }, 150);
    }, 350);
  });

  it('should not show tooltip when userStory is null', (done) => {
    component.userStory = null;
    fixture.detectChanges();

    const element = directiveElement.nativeElement;
    element.dispatchEvent(new MouseEvent('mouseenter'));

    setTimeout(() => {
      expect(document.querySelector('app-user-story-tooltip')).toBeNull();
      done();
    }, 350);
  });

  it('should position tooltip correctly', (done) => {
    const element = directiveElement.nativeElement;

    // Positionner l'élément de test
    element.style.position = 'absolute';
    element.style.left = '100px';
    element.style.top = '100px';

    element.dispatchEvent(new MouseEvent('mouseenter'));

    setTimeout(() => {
      const tooltip = document.querySelector('app-user-story-tooltip') as HTMLElement;
      expect(tooltip).toBeTruthy();
      expect(tooltip.style.position).toBe('fixed');
      expect(tooltip.style.zIndex).toBe('1000');
      done();
    }, 350);
  });

  it('should clean up on destroy', () => {
    const element = directiveElement.nativeElement;

    // Afficher le tooltip
    element.dispatchEvent(new MouseEvent('mouseenter'));

    // Détruire le composant
    fixture.destroy();

    // Vérifier que le tooltip est nettoyé
    expect(document.querySelector('app-user-story-tooltip')).toBeNull();
  });

  it('should handle tooltip hover events', (done) => {
    const element = directiveElement.nativeElement;

    // Afficher le tooltip
    element.dispatchEvent(new MouseEvent('mouseenter'));

    setTimeout(() => {
      const tooltip = document.querySelector('app-user-story-tooltip') as HTMLElement;
      expect(tooltip).toBeTruthy();

      // Simuler mouseleave de l'élément puis mouseenter sur le tooltip
      element.dispatchEvent(new MouseEvent('mouseleave'));
      tooltip.dispatchEvent(new MouseEvent('mouseenter'));

      // Attendre plus que le délai de masquage
      setTimeout(() => {
        // Le tooltip devrait toujours être visible
        expect(document.querySelector('app-user-story-tooltip')).toBeTruthy();

        // Maintenant quitter le tooltip
        tooltip.dispatchEvent(new MouseEvent('mouseleave'));

        setTimeout(() => {
          expect(document.querySelector('app-user-story-tooltip')).toBeNull();
          done();
        }, 150);
      }, 150);
    }, 350);
  });

  afterEach(() => {
    // Nettoyer les tooltips qui pourraient rester
    const tooltips = document.querySelectorAll('app-user-story-tooltip');
    tooltips.forEach(tooltip => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    });
  });
});
