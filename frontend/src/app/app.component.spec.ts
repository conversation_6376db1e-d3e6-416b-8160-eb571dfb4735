import { TestBed, ComponentFixture } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

import { AppComponent } from './app.component';
import { PlanifyDataService } from './services/planify-data.service';
import { MOCK_SPRINTS, MOCK_WORK_ITEMS } from './testing/test-data';
import {WorkItem} from './interfaces/work-item';

/**
 * Tests unitaires pour AppComponent
 *
 * COUVERTURE MÉTIER :
 * - Orchestration de la planification
 * - Gestion des états d'interface (chargement, erreur, succès)
 * - Sélection et changement de sprint
 * - Intégration avec le service de données
 *
 * JUSTIFICATION :
 * Ces tests couvrent la logique métier principale de l'application :
 * le workflow de sélection de sprint et l'affichage des données de planification.
 */

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let mockService: jasmine.SpyObj<PlanifyDataService>;



  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PlanifyDataService', ['getSprints', 'getSprintFeatures']);

    await TestBed.configureTestingModule({
      imports: [AppComponent, HttpClientTestingModule, FormsModule],
      providers: [{ provide: PlanifyDataService, useValue: spy }]
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    mockService = TestBed.inject(PlanifyDataService) as jasmine.SpyObj<PlanifyDataService>;

    mockService.getSprints.and.returnValue(of(MOCK_SPRINTS));
    mockService.getSprintFeatures.and.returnValue(of(MOCK_WORK_ITEMS));
  });

  /**
   * PERTINENCE : Initialisation correcte de l'état de l'application,
   * essentiel pour l'UX de chargement
   */
  it('devrait initialiser l\'état de chargement au démarrage', () => {
    expect(component['isLoading']).toBe(true);
    expect(component['selectedSprintPath']).toBe('');
    expect(component['sprints']).toEqual([]);
    expect(component['selectedSprint']).toBeNull();
  });

  /**
   * PERTINENCE : Workflow principal - chargement automatique des sprints
   * au démarrage de l'application
   */
  it('devrait charger automatiquement les sprints disponibles', () => {
    component.ngOnInit();
    expect(mockService.getSprints).toHaveBeenCalled();
  });

  /**
   * PERTINENCE : Gestion d'état après chargement réussi des sprints,
   * permet à l'utilisateur de sélectionner un sprint
   */
  it('devrait mettre à jour l\'état après chargement des sprints', () => {
    component['fetchSprints']();

    expect(component['sprints']).toEqual(MOCK_SPRINTS);
    expect(component['isLoading']).toBe(false);
    expect(component['errorMessage']).toBeNull();
  });

  /**
   * PERTINENCE : Gestion d'erreur critique - l'utilisateur doit être informé
   * si les sprints ne peuvent pas être chargés
   */
  it('devrait afficher une erreur si le chargement des sprints échoue', () => {
    mockService.getSprints.and.returnValue(throwError(() => new Error('Erreur réseau')));

    component['fetchSprints']();

    expect(component['errorMessage']).toContain('Erreur réseau');
    expect(component['isLoading']).toBe(false);
    expect(component['sprints']).toEqual([]);
  });

  /**
   * PERTINENCE : Workflow de sélection de sprint - fonctionnalité centrale
   * qui déclenche l'affichage de la timeline de planification
   */
  it('devrait charger les données du sprint sélectionné', () => {
    component['sprints'] = MOCK_SPRINTS;
    component['selectedSprintPath'] = 'Project\\Sprint 1';

    component['onSprintChange']();

    expect(component['selectedSprint']).toEqual(MOCK_SPRINTS[0]);
    expect(mockService.getSprintFeatures).toHaveBeenCalledWith('Project\\Sprint 1');
    expect(component['selectedSprintFeatures']).toEqual(MOCK_WORK_ITEMS);
  });

  /**
   * PERTINENCE : Validation que l'interface calendrier est affichée
   * quand toutes les données sont disponibles
   */
  it('devrait activer l\'affichage du calendrier avec les données complètes', () => {
    component['selectedSprint'] = MOCK_SPRINTS[0];
    component['selectedSprintFeatures'] = MOCK_WORK_ITEMS;
    component['isLoading'] = false;
    component['errorMessage'] = null;

    // Vérification que les conditions d'affichage du calendrier sont remplies
    expect(component['selectedSprint']).toBeTruthy();
    expect(component['selectedSprintFeatures'].length).toBeGreaterThan(0);
    expect(component['isLoading']).toBe(false);
    expect(component['errorMessage']).toBeNull();
  });

  /**
   * PERTINENCE : Validation de la fonctionnalité de recherche de features
   */
  it('devrait filtrer les features lors de la recherche', () => {
    // Setup des données de test
    const testWorkItems: WorkItem[] = [
      {
        id: 1,
        rev: 1,
        fields: {
          'System.Id': 1,
          'System.Title': 'Authentification utilisateur',
          'System.WorkItemType': 'Feature',
          'System.State': 'Active',
          'System.CreatedDate': '2024-01-01T00:00:00Z'
        },
        url: 'http://test.com/workitem1'
      },
      {
        id: 2,
        rev: 1,
        fields: {
          'System.Id': 2,
          'System.Title': 'Gestion des profils',
          'System.WorkItemType': 'Feature',
          'System.State': 'New',
          'System.CreatedDate': '2024-01-01T00:00:00Z'
        },
        url: 'http://test.com/workitem2'
      }
    ];

    // Initialiser les données
    component['allSprintFeatures'] = testWorkItems;
    component['searchTerm'] = 'auth';

    // Exécuter la recherche
    component.performSearch();

    // Vérifier que seule la feature correspondante est retournée
    const filteredFeatures = component['selectedSprintFeatures'].filter(
      item => item.fields['System.WorkItemType'] === 'Feature'
    );
    expect(filteredFeatures.length).toBe(1);
    expect(filteredFeatures[0].fields['System.Title']).toBe('Authentification utilisateur');
  });
});
