# dev-run.ps1
# Script simple pour lancer Planify (backend Flask + frontend Angular) sous PowerShell

Write-Host "🚀 Démarrage du backend Flask..."
Start-Process powershell -ArgumentList "cd backend/src; python app.py"

Write-Host "🚀 Démarrage du frontend Angular..."
Start-Process powershell -ArgumentList "cd frontend; ng serve"

Write-Host "✅ Backend dispo sur http://localhost:5000"
Write-Host "✅ Frontend dispo sur http://localhost:4200"
Write-Host "Appuyez sur Ctrl+C dans les fenêtres pour arrêter les serveurs."
