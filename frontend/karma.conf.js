// Karma configuration file for Planify project
// This configuration ensures comprehensive test coverage reporting for academic validation

module.exports = function (config) {
  config.set({
    basePath: '',
    frameworks: ['jasmine', '@angular-devkit/build-angular'],
    plugins: [
      require('karma-jasmine'),
      require('karma-chrome-launcher'),
      require('karma-jasmine-html-reporter'),
      require('karma-coverage'),
      require('karma-junit-reporter'),
      require('@angular-devkit/build-angular/plugins/karma')
    ],
    client: {
      jasmine: {
        // you can add configuration options for <PERSON> here
        // the possible options are listed at https://jasmine.github.io/api/edge/Configuration.html
        // for example, you can disable the random execution order
        random: true
      },
      clearContext: false // leave Jasmine Spec Runner output visible in browser
    },
    jasmineHtmlReporter: {
      suppressAll: true // removes the duplicated traces
    },
    coverageReporter: {
      dir: require('path').join(__dirname, './coverage/planify'),
      subdir: '.',
      reporters: [
        { type: 'html' },
        { type: 'text-summary' },
        { type: 'lcov' },
        { type: 'cobertura' },
        { type: 'json-summary' }
      ],
      // Coverage thresholds for academic validation
      check: {
        global: {
          statements: 80,
          branches: 75,
          functions: 80,
          lines: 80
        },
        each: {
          statements: 70,
          branches: 65,
          functions: 70,
          lines: 70
        }
      },
      // Watermarks for coverage visualization
      watermarks: {
        statements: [75, 80],
        functions: [70, 75],
        branches: [55, 60],
        lines: [75, 80]
      },
      // Include all source files for accurate coverage
      includeAllSources: true,
      // Exclude test files and node_modules from coverage
      exclude: [
        'src/**/*.spec.ts',
        'src/**/*.test.ts',
        'src/test.ts',
        'src/polyfills.ts',
        'src/environments/**',
        'src/main.ts',
        'node_modules/**'
      ]
    },
    junitReporter: {
      outputDir: 'coverage/planify',
      outputFile: 'junit.xml',
      suite: 'planify-frontend',
      useBrowserName: false,
      nameFormatter: undefined,
      classNameFormatter: undefined,
      properties: {}
    },
    reporters: ['progress', 'kjhtml', 'coverage', 'junit'],
    port: 9876,
    colors: true,
    logLevel: config.LOG_INFO,
    autoWatch: true,
    browsers: ['Chrome'],
    singleRun: false,
    restartOnFileChange: true,

    // Custom configuration for CI/CD environments
    customLaunchers: {
      ChromeHeadlessCI: {
        base: 'ChromeHeadless',
        flags: [
          '--no-sandbox',
          '--disable-web-security',
          '--disable-gpu',
          '--remote-debugging-port=9222'
        ]
      }
    },

    // Browser timeout settings
    browserDisconnectTimeout: 10000,
    browserDisconnectTolerance: 3,
    browserNoActivityTimeout: 60000,
    captureTimeout: 60000,

    // Test file patterns
    files: [
      'src/**/*.spec.ts'
    ],

    // Preprocessors
    preprocessors: {
      'src/**/*.ts': ['coverage']
    }
  });

  // Use ChromeHeadless in CI environment
  if (process.env.CI) {
    config.browsers = ['ChromeHeadlessCI'];
    config.singleRun = true;
    config.autoWatch = false;
  }
};
