
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">74.88% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>313/418</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.85% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>70/121</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">77.08% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>74/96</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.5% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>306/400</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="app"><a href="app/index.html">app</a></td>
	<td data-value="86.79" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.79" class="pct high">86.79%</td>
	<td data-value="53" class="abs high">46/53</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="12" class="abs medium">6/12</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="16" class="abs high">14/16</td>
	<td data-value="88.23" class="pct high">88.23%</td>
	<td data-value="51" class="abs high">45/51</td>
	</tr>

<tr>
	<td class="file medium" data-value="app/components/feature-timeline-calendar"><a href="app/components/feature-timeline-calendar/index.html">app/components/feature-timeline-calendar</a></td>
	<td data-value="66.78" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 66%"></div><div class="cover-empty" style="width: 34%"></div></div>
	</td>
	<td data-value="66.78" class="pct medium">66.78%</td>
	<td data-value="280" class="abs medium">187/280</td>
	<td data-value="54.21" class="pct medium">54.21%</td>
	<td data-value="83" class="abs medium">45/83</td>
	<td data-value="68.25" class="pct medium">68.25%</td>
	<td data-value="63" class="abs medium">43/63</td>
	<td data-value="68.67" class="pct medium">68.67%</td>
	<td data-value="265" class="abs medium">182/265</td>
	</tr>

<tr>
	<td class="file high" data-value="app/components/user-story-tooltip"><a href="app/components/user-story-tooltip/index.html">app/components/user-story-tooltip</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	</tr>

<tr>
	<td class="file high" data-value="app/directives"><a href="app/directives/index.html">app/directives</a></td>
	<td data-value="92.98" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.98" class="pct high">92.98%</td>
	<td data-value="57" class="abs high">53/57</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="92.98" class="pct high">92.98%</td>
	<td data-value="57" class="abs high">53/57</td>
	</tr>

<tr>
	<td class="file high" data-value="app/services"><a href="app/services/index.html">app/services</a></td>
	<td data-value="93.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="15" class="abs high">14/15</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="7" class="abs low">3/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="14" class="abs high">13/14</td>
	</tr>

<tr>
	<td class="file high" data-value="app/testing"><a href="app/testing/index.html">app/testing</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="environments"><a href="environments/index.html">environments</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-17T15:21:35.204Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    