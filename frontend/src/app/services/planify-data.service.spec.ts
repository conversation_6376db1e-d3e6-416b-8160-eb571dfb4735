import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { PlanifyDataService } from './planify-data.service';
import { environment } from '../../environments/environment';
import { MOCK_SPRINTS, MOCK_FEATURE_WORK_ITEMS } from '../testing/test-data';

/**
 * Tests unitaires pour PlanifyDataService
 *
 * COUVERTURE MÉTIER :
 * - Communication API pour récupération des sprints et work items
 * - Gestion d'erreurs réseau et serveur (critique pour l'UX)
 * - Validation des données reçues de l'API
 *
 * JUSTIFICATION :
 * Ces tests couvrent la couche de données, essentielle pour la fiabilité
 * de l'application de planification. La gestion d'erreur est critique
 * car l'utilisateur doit être informé si les données ne sont pas disponibles.
 */

describe('PlanifyDataService', () => {
  let service: PlanifyDataService;
  let httpMock: HttpTestingController;



  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [PlanifyDataService]
    });
    service = TestBed.inject(PlanifyDataService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  /**
   * PERTINENCE : Valide la récupération des sprints, fonctionnalité centrale
   * de l'application pour la sélection et planification
   */
  it('devrait récupérer les sprints disponibles pour la planification', () => {
    service.getSprints().subscribe(sprints => {
      expect(sprints).toEqual(MOCK_SPRINTS);
      expect(sprints[0].name).toBe('Sprint 1');
      expect(sprints[0].attributes.timeFrame).toBe('current');
    });

    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints`);
    expect(req.request.method).toBe('GET');
    req.flush(MOCK_SPRINTS);
  });

  /**
   * PERTINENCE : Gestion d'erreur critique pour l'UX - l'utilisateur doit
   * être informé si les données de planification ne sont pas disponibles
   */
  it('devrait gérer les erreurs de récupération des sprints', () => {
    service.getSprints().subscribe({
      next: () => fail('Erreur attendue'),
      error: (error) => {
        expect(error).toBeTruthy();
        expect(error.message).toBeTruthy();
      }
    });

    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints`);
    req.flush('Erreur serveur', { status: 500, statusText: 'Server Error' });
  });

  /**
   * PERTINENCE : Récupération des features/user stories d'un sprint,
   * cœur de la logique de planification et affichage timeline
   */
  it('devrait récupérer les features et user stories d\'un sprint sélectionné', () => {


    const sprintPath = 'Project\\Sprint 1';
    service.getSprintFeatures(sprintPath).subscribe(features => {
      expect(features.length).toBe(2);
      expect(features[0].fields['System.WorkItemType']).toBe('Feature');
      expect(features[1].fields['System.WorkItemType']).toBe('User Story');
      expect(features[1].fields['System.Parent']).toBe(1);
    });

    const encodedPath = encodeURIComponent(sprintPath);
    const req = httpMock.expectOne(`${environment.apiBaseUrl}/sprints/${encodedPath}/features`);
    req.flush(MOCK_FEATURE_WORK_ITEMS);
  });
});
