# Manuel d'Utilisation - Planify

## Vue d'ensemble

Planify est un outil de planification multi-sprints qui offre une interface calendaire timeline pour visualiser les Features et User Stories d'Azure DevOps Server. Cette interface permet une vue d'ensemble claire de la planification et de l'avancement des projets.

## Accès à l'application

### URL d'accès
- **Développement** : `http://localhost:4200`
- **Production** : URL configurée par votre administrateur

### Page d'accueil
Au premier accès, vous verrez le message :
> "Bienvenue dans Planify - Sélectionnez une itération pour afficher le planning des features et user stories."

## Interface principale

### En-tête de l'application

L'en-tête contient :
- **Titre** : "Planify" (côté gauche)
- **Contrôles** (côté droit) :
  - Sélecteur de sprint (dropdown)
  - Barre de recherche avec icône de recherche et bouton de nettoyage

#### Sélecteur de sprint
- Dropdown situé en haut à droite
- Affiche tous les sprints disponibles
- Format : "Nom du Sprint (DD/MM/YYYY - DD/MM/YYYY)"
- Sélection automatique du sprint actuel si disponible

#### Barre de recherche
- Champ de recherche avec placeholder "Rechercher features, user stories..."
- Icône de recherche à droite
- Icône de nettoyage (X) pour effacer la recherche
- Recherche en temps réel dans les titres des work items

## Interface calendaire timeline

### Structure générale

L'interface se compose de trois zones principales :

1. **Colonne Features** (fixe à gauche, largeur 300px)
2. **Timeline des jours** (scrollable horizontalement)
3. **Pied de page Sprint** (fixe en bas)

### Colonne Features

#### En-tête Features
- Titre "Features"
- Boutons d'actions groupées :
  - **▼** : Réduire toutes les features (si des features sont développées)
  - **▶** : Développer toutes les features (si des features sont réduites)

#### Lignes Features
Chaque feature affiche :
- **Titre de la feature** (cliquable pour développer/réduire)
- **Indicateur d'état** : ▼ (développée) ou ▶ (réduite)
- **Couleur distinctive** : Chaque feature a une couleur unique
- **Hauteur dynamique** : S'adapte au nombre d'User Stories

### Timeline des jours

#### En-têtes des jours
- **Nom du jour** : Lun, Mar, Mer, Jeu, Ven (pas de week-ends)
- **Numéro du jour** : Format DD
- **Jour actuel** : Surligné en bleu avec bordures spéciales

#### Cellules de timeline
- **Grille uniforme** : Chaque cellule représente un jour
- **Largeur fixe** : 120px par jour
- **Bordures** : Délimitent clairement chaque jour
- **Jour actuel** : Fond bleu clair (#f0f8ff) avec bordures bleues

### User Stories

#### Affichage des User Stories
Les User Stories apparaissent sous forme de barres horizontales :

- **Barres continues** : S'étendent sur plusieurs jours selon la durée
- **Couleur héritée** : Même couleur que la feature parente
- **Positionnement** : Évite les chevauchements par un système de lignes
- **Visibilité** : Masquées quand la feature est réduite

#### Contenu des barres User Story
Chaque barre affiche :
- **Titre** : Première ligne
- **Affectation** : "Affectation: [Nom de la personne]" (deuxième ligne)
- **État** : Troisième ligne

#### Tooltip détaillé
Au survol d'une User Story, un tooltip apparaît avec :
- **Titre complet** et **ID** (#123)
- **État** : Avec couleur selon l'état
- **Affectation** : Nom de la personne assignée ou "Non assigné"
- **Date de début** : Format DD/MM/YYYY
- **Date de fin** : Format DD/MM/YYYY

## Navigation et interaction

### Scroll horizontal
- **Scroll global** : Toute la timeline se déplace ensemble
- **Barre de scroll** : Positionnée en bas de l'interface
- **Synchronisation** : En-têtes et contenu bougent ensemble

### Scroll vertical
- **Scroll automatique** : Apparaît quand le contenu dépasse la hauteur disponible
- **Synchronisation** : Colonne Features et timeline bougent ensemble

### Développer/Réduire les Features

#### Actions individuelles
- **Clic sur le titre** : Développe ou réduit la feature
- **Indicateur visuel** : ▼ (développée) / ▶ (réduite)

#### Actions groupées
- **Développer tout** : Bouton ▶ dans l'en-tête Features
- **Réduire tout** : Bouton ▼ dans l'en-tête Features
- **Disponibilité** : Boutons visibles selon l'état des features

### Bouton "Aujourd'hui"

Situé dans le pied de page :
- **Fonction** : Scroll automatique vers la date actuelle
- **Disponibilité** : Actif seulement si la date actuelle est dans le sprint
- **Titre** : "Aller à la date d'aujourd'hui" ou "La date d'aujourd'hui n'est pas dans ce sprint"

## Recherche et filtrage

### Fonctionnement de la recherche
- **Recherche en temps réel** : Résultats instantanés
- **Champs recherchés** : Titres des Features et User Stories
- **Insensible à la casse** : Majuscules/minuscules ignorées
- **Recherche partielle** : Mots partiels acceptés

### Effacement de la recherche
- **Icône X** : Bouton de nettoyage à droite du champ
- **Restauration** : Affichage complet des données

## Interprétation des couleurs et états

### Couleurs des Features
Chaque feature reçoit automatiquement une couleur unique parmi :
- Bleu (#0078d4)
- Vert (#107c10)
- Rouge (#d13438)
- Violet (#8764b8)
- Orange (#ca5010)
- Turquoise (#038387)
- Lavande (#8e8cd8)
- Cyan (#00b7c3)
- Vert clair (#bad80a)

### États des User Stories

#### Couleurs d'état dans les tooltips
- **New** : Fond bleu clair (#e3f2fd), texte bleu (#1976d2)
- **Active** : Fond orange clair (#fff3e0), texte orange (#f57c00)
- **Closed** : Fond vert clair (#e8f5e8), texte vert (#2e7d32)
- **Resolved** : Fond violet clair (#f3e5f5), texte violet (#7b1fa2)
- **Autres** : Fond gris clair (#f5f5f5), texte gris (#616161)

## Informations du sprint

### Pied de page Sprint
Affiché en bas de l'écran :
- **Nom du sprint** : Titre complet
- **Dates** : Format "DD/MM/YYYY - DD/MM/YYYY"
- **Position fixe** : Toujours visible
- **Hauteur** : 70px

## Gestion des données

### Mode test
Si aucun PAT Azure DevOps n'est configuré :
- **Données de démonstration** : Affichage de données factices
- **Fonctionnalités complètes** : Toutes les fonctions disponibles
- **Indication** : Visible dans les logs backend

### Données réelles
Avec PAT configuré :
- **Synchronisation Azure DevOps** : Données en temps réel
- **Types supportés** : Features et User Stories uniquement
- **Champs utilisés** :
  - System.Title, System.State, System.WorkItemType
  - System.AssignedTo, System.IterationPath
  - Microsoft.VSTS.Scheduling.StartDate/FinishDate

## États de l'application

### Chargement
- **Indicateur** : Spinner animé
- **Message** : "Chargement des données..."

### Erreur
- **Icône** : Symbole d'avertissement
- **Message d'erreur** : Description du problème
- **Bouton** : "Actualiser" pour recharger

### Aucun sprint sélectionné
- **Message d'accueil** : Instructions pour commencer
- **Action** : Sélectionner un sprint dans le dropdown

## Conseils d'utilisation

### Optimisation de l'affichage
- **Réduire les features** : Pour une vue d'ensemble
- **Développer sélectivement** : Focus sur les features importantes
- **Utiliser la recherche** : Pour localiser rapidement des éléments

### Navigation efficace
- **Bouton "Aujourd'hui"** : Retour rapide à la date actuelle
- **Scroll horizontal** : Explorer les périodes futures/passées
- **Tooltips** : Informations détaillées sans navigation

### Interprétation visuelle
- **Couleurs cohérentes** : Même couleur pour feature et ses User Stories
- **Longueur des barres** : Proportionnelle à la durée
- **Positionnement vertical** : Évite les chevauchements

## Limitations connues

### Données supportées
- **Types** : Features et User Stories uniquement
- **Jours** : Lundi à Vendredi (pas de week-ends)
- **Période** : Limitée aux dates du sprint sélectionné

### Performance
- **Grandes quantités** : Interface optimisée pour de nombreuses User Stories
- **Scroll fluide** : Optimisations pour éviter les lags
- **Responsive** : Non supporté, interface desktop uniquement

## Support et dépannage

### Problèmes courants
- **Pas de données** : Vérifier la sélection du sprint
- **Erreur de chargement** : Contrôler la connectivité backend
- **Affichage incorrect** : Actualiser la page (F5)

### Informations de diagnostic
- **Console navigateur** : Messages d'erreur détaillés
- **Logs backend** : Disponibles pour l'administrateur
- **Health check** : Endpoint `/api/health` pour vérifier l'API
