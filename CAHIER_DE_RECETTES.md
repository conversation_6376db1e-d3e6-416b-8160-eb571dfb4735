# Cahier de Recettes - Planify

## Informations générales

**Projet** : Planify - Outil de planification multi-sprints Azure DevOps  
**Version** : 25.8.1  
**Date** : 2025-01-13  
**Environnement de test** : Production/Staging  

## Objectifs du cahier de recettes

Ce cahier de recettes valide l'ensemble des fonctionnalités de Planify selon trois axes :
- **Tests fonctionnels** : Validation des fonctionnalités métier
- **Tests structurels** : Validation de l'architecture et des performances
- **Tests de sécurité** : Validation de la sécurité et de la robustesse

## Configuration de test

### Environnement technique
- **Frontend** : Angular 19.2.0 sur http://localhost:4200
- **Backend** : Flask Python 3.11 sur http://localhost:5000
- **Base de données** : Azure DevOps Server via API REST 7.1
- **Navigateurs supportés** : Chrome, Firefox, Edge (dernières versions)

### Données de test
- **Mode test** : Données factices si PAT non configuré
- **Mode production** : Connexion Azure DevOps réelle
- **Sprints de test** : Sprint 0, Sprint 1, Sprint 2, ERP modules
- **Work Items** : Features et User Stories avec états variés

### Critères de couverture
- **Couverture de code** : 80% statements, 75% branches, 80% functions
- **Tests unitaires** : Karma + Jasmine
- **Tests d'intégration** : Composants Angular + API Flask

---

## Tests Fonctionnels

### TF001 - Chargement initial de l'application

**Objectif** : Valider le démarrage correct de l'application

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF001.1 | Accéder à http://localhost:4200 | Page d'accueil affichée avec titre "Planify" | ⏳ |
| TF001.2 | Vérifier l'état initial | Spinner de chargement visible avec message "Chargement..." | ⏳ |
| TF001.3 | Attendre la fin du chargement | Message d'accueil "Bienvenue dans Planify" affiché | ⏳ |
| TF001.4 | Vérifier les contrôles | Sélecteur de sprint visible avec option "-- Sélectionner --" | ⏳ |

**Critères de validation** :
- Temps de chargement < 3 secondes
- Aucune erreur console JavaScript
- Interface responsive et fonctionnelle

### TF002 - Sélection et chargement d'un sprint

**Objectif** : Valider la sélection d'un sprint et le chargement des données

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF002.1 | Cliquer sur le sélecteur de sprint | Liste déroulante avec sprints disponibles | ⏳ |
| TF002.2 | Vérifier le filtrage | Seuls les sprints "current" et "future" visibles | ⏳ |
| TF002.3 | Sélectionner "Sprint 1 - Développement Core" | Chargement automatique des données | ⏳ |
| TF002.4 | Vérifier l'affichage | Interface timeline affichée avec features et user stories | ⏳ |
| TF002.5 | Vérifier le pied de page | Informations du sprint affichées (nom, dates) | ⏳ |

**Critères de validation** :
- Appel API `/api/sprints/{path}/features` effectué
- Données affichées dans la timeline
- Pied de page mis à jour

### TF003 - Interface calendaire timeline

**Objectif** : Valider l'affichage et la navigation dans la timeline

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF003.1 | Observer la structure | Colonne Features (300px) + Timeline scrollable | ⏳ |
| TF003.2 | Vérifier les en-têtes | Jours de la semaine (Lun-Ven) avec dates | ⏳ |
| TF003.3 | Identifier le jour actuel | Jour actuel surligné en bleu (#f0f8ff) | ⏳ |
| TF003.4 | Vérifier les features | Features listées avec couleurs distinctives | ⏳ |
| TF003.5 | Observer les user stories | Barres horizontales colorées selon la feature parente | ⏳ |

**Critères de validation** :
- Grille CSS uniforme et alignée
- Couleurs cohérentes (9 couleurs prédéfinies)
- Synchronisation des en-têtes et contenu

### TF004 - Fonctionnalités d'interaction

**Objectif** : Valider les interactions utilisateur avec la timeline

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF004.1 | Cliquer sur une feature | Feature développée/réduite avec indicateur ▼/▶ | ⏳ |
| TF004.2 | Utiliser "Développer tout" | Toutes les features développées | ⏳ |
| TF004.3 | Utiliser "Réduire tout" | Toutes les features réduites, user stories masquées | ⏳ |
| TF004.4 | Scroll horizontal | Timeline défile, en-têtes synchronisés | ⏳ |
| TF004.5 | Cliquer "Aujourd'hui" | Scroll automatique vers la date actuelle | ⏳ |

**Critères de validation** :
- État des features persistant
- Scroll fluide sans lag
- Bouton "Aujourd'hui" actif si date dans le sprint

### TF005 - Tooltips des User Stories

**Objectif** : Valider l'affichage des informations détaillées

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF005.1 | Survoler une user story | Tooltip apparaît après 300ms | ⏳ |
| TF005.2 | Vérifier le contenu | Titre, ID, État, Affectation, Dates | ⏳ |
| TF005.3 | Vérifier les couleurs d'état | État coloré selon New/Active/Closed/Resolved | ⏳ |
| TF005.4 | Tester le positionnement | Tooltip ajusté si bord d'écran | ⏳ |
| TF005.5 | Quitter la zone | Tooltip disparaît après 100ms | ⏳ |

**Critères de validation** :
- Délais respectés (300ms show, 100ms hide)
- Positionnement intelligent
- Contenu complet et formaté

### TF006 - Fonctionnalité de recherche

**Objectif** : Valider la recherche dans les features et user stories

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF006.1 | Saisir "Authentification" | Filtrage en temps réel des résultats | ⏳ |
| TF006.2 | Vérifier le filtrage | Seules les features correspondantes affichées | ⏳ |
| TF006.3 | Tester recherche par ID | Saisir "1001", feature correspondante affichée | ⏳ |
| TF006.4 | Cliquer icône de nettoyage (✕) | Recherche effacée, tous les éléments affichés | ⏳ |
| TF006.5 | Tester recherche vide | Aucun résultat si terme inexistant | ⏳ |

**Critères de validation** :
- Recherche insensible à la casse
- Recherche partielle dans titres
- Recherche exacte/partielle dans IDs

### TF007 - Gestion des états d'erreur

**Objectif** : Valider la gestion des erreurs et la récupération

| Étape | Action | Résultat attendu | Statut |
|-------|--------|------------------|--------|
| TF007.1 | Simuler erreur réseau | Message d'erreur avec icône ⚠ | ⏳ |
| TF007.2 | Vérifier le bouton | Bouton "Actualiser" disponible | ⏳ |
| TF007.3 | Cliquer "Actualiser" | Rechargement de la page (F5) | ⏳ |
| TF007.4 | Tester PAT invalide | Mode test activé avec données factices | ⏳ |
| TF007.5 | Vérifier les logs | Messages d'information dans les logs backend | ⏳ |

**Critères de validation** :
- Messages d'erreur explicites
- Possibilité de récupération
- Fallback en mode test

---

## Tests Structurels

### TS001 - Performance de l'interface

**Objectif** : Valider les performances de l'application

| Métrique | Valeur cible | Méthode de mesure | Statut |
|----------|--------------|-------------------|--------|
| TS001.1 | Temps de chargement initial < 3s | Mesure navigateur | ⏳ |
| TS001.2 | Taille bundle < 1MB | `ng build --stats-json` | ⏳ |
| TS001.3 | Scroll fluide > 30fps | DevTools Performance | ⏳ |
| TS001.4 | Mémoire < 50MB | DevTools Memory | ⏳ |
| TS001.5 | Tooltip responsive < 100ms | Mesure interaction | ⏳ |

### TS002 - Architecture et qualité du code

**Objectif** : Valider la qualité structurelle du code

| Test | Commande | Seuil | Statut |
|------|----------|-------|--------|
| TS002.1 | `npm test` | Tous les tests passent | ⏳ |
| TS002.2 | `npm run test:coverage` | 80% statements | ⏳ |
| TS002.3 | `npm run test:coverage` | 75% branches | ⏳ |
| TS002.4 | `npm run test:coverage` | 80% functions | ⏳ |
| TS002.5 | `ng build` | Build sans erreur | ⏳ |

### TS003 - API Backend et intégration

**Objectif** : Valider la robustesse de l'API

| Endpoint | Test | Résultat attendu | Statut |
|----------|------|------------------|--------|
| TS003.1 | `GET /api/health` | Status 200, JSON valide | ⏳ |
| TS003.2 | `GET /api/sprints` | Liste des sprints | ⏳ |
| TS003.3 | `GET /api/sprints/{path}/features` | Work items du sprint | ⏳ |
| TS003.4 | Health check Docker | Container healthy | ⏳ |
| TS003.5 | Logs sans erreur | Aucune erreur critique | ⏳ |

### TS004 - Compatibilité navigateurs

**Objectif** : Valider le support multi-navigateurs

| Navigateur | Version | Tests fonctionnels | Statut |
|------------|---------|-------------------|--------|
| TS004.1 | Chrome | Dernière | TF001-TF007 | ⏳ |
| TS004.2 | Firefox | Dernière | TF001-TF007 | ⏳ |
| TS004.3 | Edge | Dernière | TF001-TF007 | ⏳ |
| TS004.4 | Safari | Dernière (si disponible) | TF001-TF007 | ⏳ |

---

## Tests de Sécurité

### TS001 - Sécurité des communications

**Objectif** : Valider la sécurité des échanges de données

| Test | Vérification | Résultat attendu | Statut |
|------|--------------|------------------|--------|
| TS001.1 | CORS configuré | Headers CORS présents | ⏳ |
| TS001.2 | Authentification PAT | Token non exposé côté client | ⏳ |
| TS001.3 | HTTPS en production | SSL/TLS activé | ⏳ |
| TS001.4 | Variables d'environnement | Secrets non versionnés | ⏳ |
| TS001.5 | Headers de sécurité | CSP, X-Frame-Options | ⏳ |

### TS002 - Validation des entrées

**Objectif** : Valider la robustesse contre les injections

| Test | Input | Comportement attendu | Statut |
|------|-------|---------------------|--------|
| TS002.1 | Recherche avec `<script>` | Échappement HTML | ⏳ |
| TS002.2 | Sprint path avec `../` | Validation côté serveur | ⏳ |
| TS002.3 | Caractères spéciaux | Encodage URL correct | ⏳ |
| TS002.4 | Payload JSON malformé | Erreur 400 gérée | ⏳ |
| TS002.5 | Requêtes trop longues | Timeout approprié | ⏳ |

### TS003 - Gestion des erreurs et logs

**Objectif** : Valider la sécurité des informations d'erreur

| Test | Scénario | Résultat attendu | Statut |
|------|----------|------------------|--------|
| TS003.1 | PAT invalide | Message générique | ⏳ |
| TS003.2 | Erreur Azure DevOps | Pas d'exposition d'infos sensibles | ⏳ |
| TS003.3 | Logs backend | Pas de secrets dans les logs | ⏳ |
| TS003.4 | Stack traces | Non exposées côté client | ⏳ |
| TS003.5 | Rate limiting | Protection contre le spam | ⏳ |

---

## Procédures d'exécution

### Préparation de l'environnement

```bash
# 1. Démarrage des services
docker-compose up -d

# 2. Vérification des services
curl http://localhost:5000/api/health
curl http://localhost:4200

# 3. Configuration des données de test
# Utiliser mode test ou configurer PAT Azure DevOps
```

### Exécution des tests automatisés

```bash
# Tests unitaires frontend
cd frontend
npm test

# Tests de couverture
npm run test:coverage

# Vérification des seuils
npm run coverage:check

# Build de production
npm run build
```

### Validation manuelle

1. **Tests fonctionnels** : Suivre les étapes TF001-TF007
2. **Tests structurels** : Exécuter les commandes TS001-TS004
3. **Tests de sécurité** : Valider les points TS001-TS003

### Critères de validation globale

**Succès** : Tous les tests marqués ✅  
**Échec partiel** : Tests critiques ✅, tests secondaires ⚠️  
**Échec** : Tests critiques ❌

**Tests critiques** :
- TF001, TF002, TF003 (fonctionnalités de base)
- TS001, TS002 (performance et qualité)
- TS001, TS002 (sécurité de base)

---

## Tests de Régression

### TR001 - Régression après mise à jour Angular

**Objectif** : Valider la stabilité après mise à jour des dépendances

| Test | Fonctionnalité | Validation | Statut |
|------|----------------|------------|--------|
| TR001.1 | Compilation TypeScript | `ng build` sans erreur | ⏳ |
| TR001.2 | Tests unitaires | Tous les tests passent | ⏳ |
| TR001.3 | Interface timeline | Affichage correct | ⏳ |
| TR001.4 | Interactions utilisateur | Fonctionnalités préservées | ⏳ |
| TR001.5 | Performance | Pas de dégradation | ⏳ |

### TR002 - Régression après mise à jour Backend

**Objectif** : Valider la compatibilité API après mise à jour Python/Flask

| Test | Endpoint | Validation | Statut |
|------|----------|------------|--------|
| TR002.1 | `/api/health` | Réponse identique | ⏳ |
| TR002.2 | `/api/sprints` | Format JSON préservé | ⏳ |
| TR002.3 | `/api/sprints/{path}/features` | Données complètes | ⏳ |
| TR002.4 | Gestion erreurs | Codes HTTP corrects | ⏳ |
| TR002.5 | Performance API | Temps de réponse < 2s | ⏳ |

### TR003 - Régression après mise à jour Docker

**Objectif** : Valider le déploiement après mise à jour des images de base

| Test | Composant | Validation | Statut |
|------|-----------|------------|--------|
| TR003.1 | Image frontend | Build et démarrage | ⏳ |
| TR003.2 | Image backend | Health check OK | ⏳ |
| TR003.3 | Docker Compose | Services communicants | ⏳ |
| TR003.4 | Variables d'environnement | Configuration préservée | ⏳ |
| TR003.5 | Volumes et réseaux | Persistance des données | ⏳ |

---

## Annexes

### Annexe A - Commandes de test

```bash
# Tests unitaires complets
cd frontend
npm test -- --watch=false --browsers=ChromeHeadless

# Tests avec couverture
npm run test:coverage

# Vérification des seuils de couverture
npm run coverage:check

# Tests d'intégration API
curl -X GET http://localhost:5000/api/health
curl -X GET http://localhost:5000/api/sprints
curl -X GET "http://localhost:5000/api/sprints/MyProject%5CSprint%201/features"

# Tests de performance
time npm run build
docker stats --no-stream

# Tests de sécurité
# Vérification des headers CORS
curl -H "Origin: http://localhost:4200" -v http://localhost:5000/api/health

# Test d'injection XSS (doit être échappé)
curl -X GET "http://localhost:5000/api/sprints/<script>alert('xss')</script>/features"
```

### Annexe B - Données de test

**Sprints disponibles** :
- Sprint 0 - Initialisation (past)
- Sprint 1 - Développement Core (current)
- Sprint 2 - Interface Utilisateur (future)
- ERP - Module Facturation (future)
- ERP - Gestion des stocks (future)

**Work Items types** :
- Features : Authentification, Interface, API, Tests
- User Stories : Login, Dashboard, Endpoints, Unit Tests
- États : New, Active, Closed, Resolved

**Utilisateurs de test** :
- Sophie Leroy, Luc Moreau, Jean Dupont, Marie Martin, Pierre Durand

### Annexe C - Critères de performance

| Métrique | Valeur cible | Méthode |
|----------|--------------|---------|
| First Contentful Paint | < 1.5s | Lighthouse |
| Largest Contentful Paint | < 2.5s | Lighthouse |
| Time to Interactive | < 3s | Lighthouse |
| Bundle size | < 1MB | webpack-bundle-analyzer |
| API Response Time | < 2s | curl -w "%{time_total}" |
| Memory Usage | < 50MB | Chrome DevTools |

### Annexe D - Checklist de validation

**Avant exécution** :
- [ ] Environnement Docker démarré
- [ ] Services accessibles (ports 4200, 5000)
- [ ] Configuration Azure DevOps validée
- [ ] Navigateurs de test disponibles
- [ ] Outils de mesure installés

**Pendant exécution** :
- [ ] Logs surveillés en temps réel
- [ ] Screenshots des anomalies
- [ ] Métriques de performance collectées
- [ ] Temps d'exécution mesurés

**Après exécution** :
- [ ] Rapport de test complété
- [ ] Anomalies documentées
- [ ] Actions correctives planifiées
- [ ] Validation finale signée

---

## Suivi et traçabilité

### Matrice de traçabilité

| Fonctionnalité | Tests Fonctionnels | Tests Structurels | Tests Sécurité |
|----------------|-------------------|-------------------|----------------|
| Chargement application | TF001 | TS001, TS002 | TS001 |
| Sélection sprint | TF002 | TS003 | TS002 |
| Timeline interface | TF003, TF004 | TS001, TS004 | - |
| Tooltips | TF005 | TS001 | TS002 |
| Recherche | TF006 | TS002 | TS002 |
| Gestion erreurs | TF007 | TS003 | TS003 |

### Rapport d'exécution

**Date d'exécution** : ___________
**Testeur** : ___________
**Environnement** : ___________
**Version testée** : ___________

**Résultats** :
- Tests fonctionnels : ___/7 ✅
- Tests structurels : ___/4 ✅
- Tests de sécurité : ___/3 ✅
- Tests de régression : ___/3 ✅

**Métriques de qualité** :
- Couverture de code : ___%
- Performance (LCP) : ___s
- Temps de build : ___s
- Taille bundle : ___MB

**Anomalies détectées** : ___________
**Actions correctives** : ___________
**Validation finale** : ✅ / ❌

**Signatures** :
- Testeur : ___________
- Responsable technique : ___________
- Chef de projet : ___________
