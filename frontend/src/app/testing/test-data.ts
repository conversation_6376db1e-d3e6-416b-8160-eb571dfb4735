import { Sprint } from '../interfaces/sprint';
import { WorkItem } from '../interfaces/work-item';

/**
 * Shared test data for Planify unit tests
 * Centralizes mock data to avoid duplication across test files
 */

export const MOCK_SPRINTS: Sprint[] = [
  {
    id: 'sprint-1',
    name: 'Sprint 1',
    path: 'Project\\Sprint 1',
    attributes: {
      startDate: '2024-01-01T00:00:00Z',
      finishDate: '2024-01-14T23:59:59Z',
      timeFrame: 'current'
    },
    url: 'http://test.com/sprint1'
  },
  {
    id: 'sprint-2',
    name: 'Sprint 2',
    path: 'Project\\Sprint 2',
    attributes: {
      startDate: '2024-01-15T00:00:00Z',
      finishDate: '2024-01-28T23:59:59Z',
      timeFrame: 'future'
    },
    url: 'http://test.com/sprint2'
  }
];

export const MOCK_WORK_ITEMS: WorkItem[] = [
  {
    id: 1,
    rev: 1,
    fields: {
      'System.Id': 1,
      'System.Title': 'Feature Authentification',
      'System.WorkItemType': 'Feature',
      'System.State': 'Active',
      'System.CreatedDate': '2024-01-01T00:00:00Z',
      'System.IterationPath': 'Project\\Sprint 1'
    },
    url: 'http://test.com/workitem1'
  },
  {
    id: 2,
    rev: 1,
    fields: {
      'System.Id': 2,
      'System.Title': 'Login utilisateur',
      'System.WorkItemType': 'User Story',
      'System.State': 'New',
      'System.Parent': 1,
      'System.CreatedDate': '2024-01-01T00:00:00Z',
      'System.IterationPath': 'Project\\Sprint 1',
      'System.AssignedTo': {
        displayName: 'John Doe',
        uniqueName: '<EMAIL>'
      },
      'Microsoft.VSTS.Scheduling.StartDate': '2024-01-02T00:00:00Z',
      'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-05T00:00:00Z'
    },
    url: 'http://test.com/workitem2'
  }
];

export const MOCK_FEATURE_WORK_ITEMS: WorkItem[] = [
  {
    id: 1,
    rev: 1,
    fields: {
      'System.Id': 1,
      'System.Title': 'Authentification utilisateur',
      'System.WorkItemType': 'Feature',
      'System.State': 'Active',
      'System.CreatedDate': '2024-01-01T00:00:00Z',
      'System.IterationPath': 'Project\\Sprint 1'
    },
    url: 'http://test.com/workitem1'
  },
  {
    id: 2,
    rev: 1,
    fields: {
      'System.Id': 2,
      'System.Title': 'Login utilisateur',
      'System.WorkItemType': 'User Story',
      'System.State': 'New',
      'System.Parent': 1,
      'System.CreatedDate': '2024-01-01T00:00:00Z',
      'System.IterationPath': 'Project\\Sprint 1',
      'Microsoft.VSTS.Scheduling.StartDate': '2024-01-02T00:00:00Z',
      'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-05T00:00:00Z'
    },
    url: 'http://test.com/workitem2'
  }
];
