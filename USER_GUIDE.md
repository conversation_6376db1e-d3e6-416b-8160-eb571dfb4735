# Planify - Guide Utilisateur

## 🎯 Introduction

Planify est votre outil de planification visuelle pour Azure DevOps Server. Il vous permet de visualiser et organiser vos Features, User Stories et Tasks dans une interface calendaire intuitive et moderne, offrant une vue d'ensemble claire de la planification multi-sprints.

## 🚀 Accès et démarrage

### Accès à l'application
- **Développement**: `http://localhost:4200`
- **Production**: URL configurée par votre administrateur

### Premier pas
1. Ouvrez votre navigateur web
2. Accédez à l'URL Planify de votre organisation
3. **Sélectionnez un sprint** dans le menu déroulant "Itération" en haut à droite
4. **Attendez le chargement** des données (indicateur de progression visible)
5. **Explorez la timeline** qui s'affiche automatiquement

## 📅 Interface principale

L'interface Planify se compose de trois zones principales:

```
┌─────────────────────────────────────────────────────────────┐
│  Planify                [Recherche ⌕]        [Itération ▼]   │ ← En-tête
├─────────────────────────────────────────────────────────────┤
│ Features    │  Lun │ Mar │ Mer │ Jeu │ Ven │ Lun │ Mar │... │ ← Timeline
│ [▼][▶]      │   1  │  2  │  3  │  4  │  5  │  8  │  9  │    │
├─────────────┼──────┼─────┼─────┼─────┼─────┼─────┼─────┼────┤
│ Feature A ▼ │      │ ████████████████████ │     │     │    │ ← User Stories
│ #123        │      │ User Story 1         │     │     │    │
│             │      │ Affectation: John    │     │     │    │
├─────────────┼──────┼─────┼─────┼─────┼─────┼─────┼─────┼────┤
│ Feature B ▶ │ ██████████████ │           │     │     │    │
│ #124        │                │           │     │     │    │
│             │                │           │     │     │    │
└─────────────┴──────┴─────┴─────┴─────┴─────┴─────┴─────┴────┘
   Sprint: Nom Sprint (01/05/2023 - 12/05/2023) [Aujourd'hui]   ← Pied de page
```

### Zone en-tête
- **Titre Planify**: Identification de l'application (côté gauche)
- **Sélecteur d'Itération**: Menu déroulant pour choisir le sprint actif
- **Barre de recherche**: Champ de recherche avec placeholder "Rechercher features, user stories..."
- **Indicateurs d'État**: Messages de chargement ou d'erreur

### Zone features (colonne fixe)
- **En-tête Features**: Titre "Features" avec boutons d'actions groupées (▼ Réduire tout, ▶ Développer tout)
- **Titre de la Feature**: Nom complet (cliquable pour développer/réduire)
- **ID**: Numéro d'identification Azure DevOps
- **Indicateur d'état**: ▼ (développée) ou ▶ (réduite)
- **Couleur**: Code couleur unique par Feature

### Zone timeline calendaire (scrollable)
- **En-têtes de jours**: Jours ouvrés uniquement (Lundi à Vendredi)
- **Numéros de jours**: Format JJ/MM
- **Barres User Stories**: Représentation visuelle des User Stories
- **Indicateur "Aujourd'hui"**: Mise en évidence du jour actuel (fond bleu clair, bordures bleues)

### Pied de page Sprint
- **Nom du sprint**: Titre complet
- **Dates**: Format "DD/MM/YYYY - DD/MM/YYYY"
- **Bouton "Aujourd'hui"**: Scroll automatique vers la date du jour

## 🖱️ Navigation et interactions

### Navigation horizontale
- **Scroll horizontal**: Utilisez la molette de la souris, la barre de défilement un le pad tactile
- **Scroll synchronisé**: Les en-têtes et le contenu bougent ensemble
- **Bouton "Aujourd'hui"**: Déplace la vue vers le jour actuel

### Navigation verticale
- **Scroll vertical**: Parcourez les Features quand elles sont nombreuses
- **Colonne fixe**: La colonne Features reste visible pendant le scroll horizontal

### Développer/Réduire les Features
- **Actions groupées**: Boutons ▼ (Réduire tout) et ▶ (Développer tout) dans l'en-tête Features, possible en individuel aussi

### Informations détaillées
- **Survol**: Passez la souris sur une User Story pour voir les détails
- **Tooltip**: Affiche le titre complet, l'ID, l'état, l'assignation et les dates de début/fin

## 📊 Gestion des sprints

### Sélection d'un sprint
1. **Cliquez** sur le menu déroulant "Itération"
2. **Choisissez** le sprint désiré dans la liste (format: "Nom du Sprint (DD/MM/YYYY - DD/MM/YYYY)")
3. **Attendez** le chargement automatique des données
4. **Visualisez** les Features et User Stories du sprint

### Types de sprints affichés
- **Sprints actuels**: En cours d'exécution
- **Sprints futurs**: Planifiés mais non démarrés
- **Sprints récents**: Terminés récemment

## 📋 Types d'éléments

### Features
- **Rôle**: Éléments principaux de planification
- **Affichage**: Une ligne par Feature dans la colonne fixe
- **Contenu**: Titre, ID, état et couleur distinctive
- **Interaction**: Développement/réduction via clic sur le titre
- **Hauteur dynamique**: S'adapte au nombre d'User Stories

### User stories
- **Rôle**: Éléments de développement détaillés
- **Affichage**: Barres horizontales colorées sur la timeline
- **Contenu**: Titre (1ère ligne), assignation (2ème ligne), état (3ème ligne)
- **Positionnement**: Alignées avec leur Feature parente
- **Visibilité**: Masquées quand la feature est réduite

## 🎨 Système de couleurs et états

### Couleurs par feature
Chaque feature possède une couleur unique parmi:
- 🔵 **Bleu**
- 🟢 **Vert**
- 🔴 **Rouge**
- 🟣 **Violet**
- 🟠 **Orange** 
- 🔷 **Turquoise**
- 🟪 **Lavande** 
- 🔵 **Cyan**
- 🟡 **Vert lime**

### États des User Stories (tooltips)
- **New**: Fond bleu clair, texte bleu
- **Active**: Fond orange clair, texte orange
- **Closed**: Fond vert clair, texte vert
- **Resolved**: Fond violet clair, texte violet
- **Autres**: Fond gris clair, texte gris

### Signification visuelle
- **Longueur de la barre**: Durée de la User Story
- **Position**: Dates de début et fin
- **Couleur**: Appartenance à une Feature
- **Hauteur**: Ligne d'affichage (évite les chevauchements)

## 🔍 Recherche et filtrage

### Fonctionnement de la recherche
- **Champ de recherche**: En haut à droite avec placeholder "Rechercher features, user stories..."
- **Recherche en temps réel**: Résultats instantanés
- **Champs recherchés**: Titres des Features et User Stories
- **Insensible à la casse**: Majuscules/minuscules ignorées
- **Recherche partielle**: Mots partiels acceptés

### Effacement de la recherche
- **Icône X**: Bouton de nettoyage à droite du champ
- **Restauration**: Affichage complet des données

## 💡 Conseils d'utilisation

### Optimisation de l'affichage
- **Utilisez un écran large** pour une meilleure visibilité
- **Réduire les features** pour une vue d'ensemble
- **Développer sélectivement** les features importantes
- **Utiliser la recherche** pour localiser rapidement des éléments

### Planification efficace
- **Définissez des dates réalistes** pour vos User Stories
- **Assignez clairement** chaque User Story
- **Utilisez les couleurs** pour identifier rapidement les Features

### Navigation efficace
- **Bouton "Aujourd'hui"**: Retour rapide à la date actuelle
- **Scroll horizontal**: Explorer les périodes futures/passées
- **Tooltips**: Informations détaillées sans navigation

### Collaboration d'équipe
- **Utilisez la vue** lors des réunions de planification
- **Mettez à jour** régulièrement Azure DevOps pour refléter les changements

## ⚠️ Résolution de problèmes

### Problèmes courants

#### "Aucune donnée affichée"
- **Vérifiez** que vous avez sélectionné un sprint
- **Confirmez** que le sprint contient des Features/User Stories
- **Actualisez** la page (F5) si nécessaire

#### "Chargement infini"
- **Patientez** quelques secondes supplémentaires
- **Vérifiez** votre connexion réseau
- **Contactez** l'administrateur si le problème persiste

#### "User Stories mal alignées"
- **Actualisez** la page pour resynchroniser l'affichage
- **Vérifiez** que les dates sont correctement définies dans Azure DevOps

### Messages d'erreur
- **"Erreur de chargement"**: Problème de connexion à Azure DevOps
- **"Sprint introuvable"**: Le sprint sélectionné n'existe plus
- **"Données corrompues"**: Problème de format des données

### Informations de diagnostic
- **Console navigateur**: Messages d'erreur détaillés (pour les développeurs)
- **Health check**: Endpoint `/api/health` pour vérifier l'API (pour les administrateurs)