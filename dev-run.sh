#!/usr/bin/env bash
# Script simple pour lancer Planify en dev sous Linux

# Lancer le backend
echo "🚀 Démarrage du backend Flask..."
cd backend/src || exit 1
python app.py &
BACK_PID=$!

# Lancer le frontend
echo "🚀 Démarrage du frontend Angular..."
cd ../../frontend || exit 1
ng serve &
FRONT_PID=$!

# Fonction d'arrêt propre
cleanup() {
  echo "🛑 Arrêt des serveurs..."
  kill $BACK_PID $FRONT_PID 2>/dev/null
  exit 0
}

trap cleanup SIGINT SIGTERM

echo "✅ Backend: http://localhost:5000"
echo "✅ Frontend: http://localhost:4200"
echo "Appuyez sur Ctrl+C pour arrêter."

# Attendre que les 2 process tournent
wait
