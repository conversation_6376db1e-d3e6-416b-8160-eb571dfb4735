[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=70
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
