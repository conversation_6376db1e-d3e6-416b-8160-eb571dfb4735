
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for app/components/feature-timeline-calendar</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> app/components/feature-timeline-calendar</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.78% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>131/280</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">34.93% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>29/83</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">53.96% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>34/63</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.67% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>129/265</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="feature-timeline-calendar.component.ts"><a href="feature-timeline-calendar.component.ts.html">feature-timeline-calendar.component.ts</a></td>
	<td data-value="46.78" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 46%"></div><div class="cover-empty" style="width: 54%"></div></div>
	</td>
	<td data-value="46.78" class="pct low">46.78%</td>
	<td data-value="280" class="abs low">131/280</td>
	<td data-value="34.93" class="pct low">34.93%</td>
	<td data-value="83" class="abs low">29/83</td>
	<td data-value="53.96" class="pct medium">53.96%</td>
	<td data-value="63" class="abs medium">34/63</td>
	<td data-value="48.67" class="pct low">48.67%</td>
	<td data-value="265" class="abs low">129/265</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-17T14:56:35.161Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    