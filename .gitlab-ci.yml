include:
  - project: "development/common/release-management/ci/gitlab-build"
    ref: "master"
    file: "common.yml"

default:
  tags:
    - docker

stages:
  - check_versions
  - define_version
  - commit_version
  - prepare_env
  - test
  - build_web_app
  - build_backend
  - build_docker
  - tag
  - publish

variables:
  BACKEND_PATH: $CI_PROJECT_DIR/backend
  BACKEND_DOCKER_PATH: $BACKEND_PATH/docker
  BACKEND_TARGET_PATH: $BACKEND_PATH/target
  BACKEND_IMAGE_NAME: planify-backend
  WEB_APP_PATH: $CI_PROJECT_DIR/frontend
  WEB_APP_DOCKER_PATH: $WEB_APP_PATH/docker
  WEB_APP_TARGET_PATH: $WEB_APP_PATH/target
  WEB_APP_IMAGE_NAME: planify-web-app

.custom prepare:
  before_script: [ ]

.custom update version:
  script: [ ]

prepare environment:
  extends: .prepare environment

.default-node-builder:
  image:
    name: itesoft/tool/node20-builder:24.10.1

.default-python-builder:
  image:
    name: python:3.11-slim

.default-docker-builder:
  extends: .dind
  image:
    name: itesoft/tool/docker-builder:25.6.1

# ===== TESTS =====

test-frontend-unit:
  stage: test
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm ci --no-audit
    - npm run test:ci
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    name: frontend-test-results
    when: always
    expire_in: 1 week
    paths:
      - $WEB_APP_PATH/coverage/
    reports:
      junit: $WEB_APP_PATH/coverage/planify/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: $WEB_APP_PATH/coverage/planify/cobertura-coverage.xml
  rules:
    - !reference [ .building_rules, rules ]

test-frontend-coverage:
  stage: test
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm ci --no-audit
    - npm run test:coverage
    - npm run coverage:check
  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
  artifacts:
    name: frontend-coverage-report
    when: always
    expire_in: 1 week
    paths:
      - $WEB_APP_PATH/coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: $WEB_APP_PATH/coverage/planify/cobertura-coverage.xml
  rules:
    - !reference [ .building_rules, rules ]

test-backend-unit:
  stage: test
  extends: .default-python-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
    - apt-get update && apt-get install -y curl
  script:
    - cd $BACKEND_PATH
    - pip install --no-cache-dir -r requirements.txt
    - pip install pytest pytest-cov pytest-flask
    - python -m pytest tests/ --cov=src --cov-report=xml --cov-report=html --cov-report=term-missing --junitxml=test-results.xml || true
    - echo "Backend tests completed (non-blocking for now)"
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    name: backend-test-results
    when: always
    expire_in: 1 week
    paths:
      - $BACKEND_PATH/htmlcov/
      - $BACKEND_PATH/coverage.xml
      - $BACKEND_PATH/test-results.xml
    reports:
      junit: $BACKEND_PATH/test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: $BACKEND_PATH/coverage.xml
  allow_failure: true
  rules:
    - !reference [ .building_rules, rules ]

test-backend-api:
  stage: test
  extends: .default-python-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
    - apt-get update && apt-get install -y curl
  script:
    - cd $BACKEND_PATH
    - pip install --no-cache-dir -r requirements.txt
    - echo "Starting Flask app in background..."
    - python src/app.py &
    - sleep 5
    - echo "Testing API endpoints..."
    - curl -f http://localhost:5000/api/health || exit 1
    - curl -f http://localhost:5000/api/sprints || exit 1
    - echo "API tests passed"
  rules:
    - !reference [ .building_rules, rules ]

test-lint-frontend:
  stage: test
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm ci --no-audit
    - npx ng lint || echo "Linting completed with warnings"
  allow_failure: true
  rules:
    - !reference [ .building_rules, rules ]

test-build-validation:
  stage: test
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm ci --no-audit
    - npm run build
    - echo "Build validation successful"
    - ls -la dist/frontend/browser/
    - du -sh dist/frontend/browser/
  artifacts:
    name: build-validation
    when: on_success
    expire_in: 1 day
    paths:
      - $WEB_APP_PATH/dist/
  rules:
    - !reference [ .building_rules, rules ]

# ===== BUILD =====

build-front:
  stage: build_web_app
  extends: .default-node-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - cd $WEB_APP_PATH
    - npm install --no-audit
    - npm run build
  artifacts:
    name: planify-web-app
    expire_in: 2 days
    paths:
      - $WEB_APP_PATH/dist
  rules:
    - !reference [ .building_rules, rules ]

build-docker-frontend:
  stage: build_docker
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - IMAGE_FULL_NAME=${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
    - echo $VERSION
    - echo $WEB_APP_IMAGE_NAME
    - echo $IMAGE_FULL_NAME
    - mkdir -p $WEB_APP_TARGET_PATH/root/usr/share/nginx/html
    - cp $WEB_APP_DOCKER_PATH/Dockerfile $WEB_APP_TARGET_PATH/
    - cp -r $WEB_APP_DOCKER_PATH/root/* $WEB_APP_TARGET_PATH/root
    - cp -r $WEB_APP_PATH/dist/frontend/browser/* $WEB_APP_TARGET_PATH/root/usr/share/nginx/html
    - docker build -t $IMAGE_FULL_NAME --build-arg="VERSION=${VERSION}" --build-arg CI_COMMIT_SHA --build-arg CI_JOB_STARTED_AT $WEB_APP_TARGET_PATH
    - docker save -o $WEB_APP_TARGET_PATH/${WEB_APP_IMAGE_NAME}-${VERSION}.tar $IMAGE_FULL_NAME
    - docker rmi $IMAGE_FULL_NAME
  artifacts:
    name: planify-web-app-image
    when: on_success
    expire_in: 2 days
    paths:
      - $WEB_APP_TARGET_PATH/*.tar
  rules:
    - !reference [ .building_rules, rules ]

build-docker-backend:
  stage: build_docker
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - IMAGE_FULL_NAME=${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
    - echo $VERSION
    - echo $BACKEND_IMAGE_NAME
    - echo $IMAGE_FULL_NAME
    - mkdir -p $BACKEND_TARGET_PATH
    - cp -r $BACKEND_PATH/requirements.txt $BACKEND_TARGET_PATH/
    - cp -r $BACKEND_PATH/src $BACKEND_TARGET_PATH/
    - cp -r $BACKEND_DOCKER_PATH/Dockerfile $BACKEND_TARGET_PATH/
    - docker build -t $IMAGE_FULL_NAME --build-arg="VERSION=${VERSION}" --build-arg CI_COMMIT_SHA --build-arg CI_JOB_STARTED_AT $BACKEND_TARGET_PATH
    - docker save -o $BACKEND_TARGET_PATH/${BACKEND_IMAGE_NAME}-${VERSION}.tar $IMAGE_FULL_NAME
    - docker rmi $IMAGE_FULL_NAME
  artifacts:
    name: planify-backend-image
    when: on_success
    expire_in: 2 days
    paths:
      - $BACKEND_TARGET_PATH/*.tar
  rules:
    - !reference [ .building_rules, rules ]

publish-docker-web-app:
  stage: publish
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - echo $VERSION
    - echo $WEB_APP_IMAGE_NAME
    - docker load -i $WEB_APP_TARGET_PATH/${WEB_APP_IMAGE_NAME}-${VERSION}.tar
    - docker login -u $DOCKER_USERNAME -p "$DOCKER_PASSWORD" $DOCKER_SERVER
    - docker push ${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
    - docker rmi ${DOCKER_SERVER}/itesoft/slfi/qa/${WEB_APP_IMAGE_NAME}:${VERSION}
  rules:
    - !reference [ .building_rules, rules ]

publish-docker-backend:
  stage: publish
  extends: .default-docker-builder
  before_script:
    - !reference [ .load environment variables, before_script ]
  script:
    - VERSION=$(cat _buildVersion)
    - echo $VERSION
    - echo $BACKEND_IMAGE_NAME
    - docker load -i $BACKEND_TARGET_PATH/${BACKEND_IMAGE_NAME}-${VERSION}.tar
    - docker login -u $DOCKER_USERNAME -p "$DOCKER_PASSWORD" $DOCKER_SERVER
    - docker push ${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
    - docker rmi ${DOCKER_SERVER}/itesoft/slfi/qa/${BACKEND_IMAGE_NAME}:${VERSION}
  rules:
    - !reference [ .building_rules, rules ]