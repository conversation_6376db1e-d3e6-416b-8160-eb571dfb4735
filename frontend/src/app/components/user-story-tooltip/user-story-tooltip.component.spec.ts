import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DatePipe } from '@angular/common';
import { UserStoryTooltipComponent } from './user-story-tooltip.component';
import { WorkItem } from '../../interfaces/work-item';

describe('UserStoryTooltipComponent', () => {
  let component: UserStoryTooltipComponent;
  let fixture: ComponentFixture<UserStoryTooltipComponent>;

  const mockUserStory: WorkItem = {
    id: 123,
    fields: {
      'System.Id': 123,
      'System.Title': 'Test User Story',
      'System.State': 'Active',
      'System.WorkItemType': 'User Story',
      'System.AssignedTo': {
        displayName: 'John Doe'
      },
      'Microsoft.VSTS.Scheduling.StartDate': '2024-01-15T00:00:00Z',
      'Microsoft.VSTS.Scheduling.FinishDate': '2024-01-20T00:00:00Z'
    },
    url: 'test-url'
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UserStoryTooltipComponent, DatePipe]
    }).compileComponents();

    fixture = TestBed.createComponent(UserStoryTooltipComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display user story information when userStory is provided', () => {
    component.userStory = mockUserStory;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.tooltip-title').textContent).toContain('Test User Story');
    expect(compiled.querySelector('.tooltip-id').textContent).toContain('#123');
  });

  it('should not display tooltip content when userStory is null', () => {
    component.userStory = null;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.tooltip-content')).toBeNull();
  });

  it('should display assigned user name', () => {
    component.userStory = mockUserStory;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const assignedToElement = compiled.querySelector('.tooltip-row:nth-child(2) .tooltip-value');
    expect(assignedToElement.textContent.trim()).toBe('John Doe');
  });

  it('should display "Non assigné" when no assigned user', () => {
    const userStoryWithoutAssignee = {
      ...mockUserStory,
      fields: {
        ...mockUserStory.fields,
        'System.AssignedTo': null
      }
    };
    component.userStory = userStoryWithoutAssignee;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const assignedToElement = compiled.querySelector('.tooltip-row:nth-child(2) .tooltip-value');
    expect(assignedToElement.textContent.trim()).toBe('Non assigné');
  });

  describe('getStateClass', () => {
    it('should return correct class for New state', () => {
      component.userStory = {
        ...mockUserStory,
        fields: { ...mockUserStory.fields, 'System.State': 'New' }
      };
      expect(component.getStateClass()).toBe('state-new');
    });

    it('should return correct class for Active state', () => {
      component.userStory = {
        ...mockUserStory,
        fields: { ...mockUserStory.fields, 'System.State': 'Active' }
      };
      expect(component.getStateClass()).toBe('state-active');
    });

    it('should return correct class for Closed state', () => {
      component.userStory = {
        ...mockUserStory,
        fields: { ...mockUserStory.fields, 'System.State': 'Closed' }
      };
      expect(component.getStateClass()).toBe('state-closed');
    });

    it('should return correct class for Resolved state', () => {
      component.userStory = {
        ...mockUserStory,
        fields: { ...mockUserStory.fields, 'System.State': 'Resolved' }
      };
      expect(component.getStateClass()).toBe('state-resolved');
    });

    it('should return default class for unknown state', () => {
      component.userStory = {
        ...mockUserStory,
        fields: { ...mockUserStory.fields, 'System.State': 'Unknown' }
      };
      expect(component.getStateClass()).toBe('state-default');
    });

    it('should return default class when userStory is null', () => {
      component.userStory = null;
      expect(component.getStateClass()).toBe('state-default');
    });
  });

  it('should apply state class to state element', () => {
    component.userStory = {
      ...mockUserStory,
      fields: { ...mockUserStory.fields, 'System.State': 'New' }
    };
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const stateElement = compiled.querySelector('.tooltip-row:nth-child(1) .tooltip-value.state');
    expect(stateElement.classList).toContain('state-new');
  });

  it('should format dates correctly', () => {
    component.userStory = mockUserStory;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const startDateElement = compiled.querySelector('.tooltip-row:nth-child(3) .tooltip-value');
    const endDateElement = compiled.querySelector('.tooltip-row:nth-child(4) .tooltip-value');
    
    // Les dates doivent être formatées en dd/MM/yyyy
    expect(startDateElement.textContent.trim()).toMatch(/\d{2}\/\d{2}\/\d{4}/);
    expect(endDateElement.textContent.trim()).toMatch(/\d{2}\/\d{2}\/\d{4}/);
  });
});
