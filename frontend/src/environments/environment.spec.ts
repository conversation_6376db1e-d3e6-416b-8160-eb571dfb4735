import { environment } from './environment';

describe('Environment', () => {
  it('should have production set to false in development', () => {
    expect(environment.production).toBe(false);
  });

  it('should have apiBaseUrl defined', () => {
    expect(environment.apiBaseUrl).toBeDefined();
    expect(typeof environment.apiBaseUrl).toBe('string');
  });

  it('should have valid API URL format', () => {
    expect(environment.apiBaseUrl).toMatch(/^https?:\/\/.+/);
  });
});
