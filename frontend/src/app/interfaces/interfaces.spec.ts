import { WorkItem } from './work-item';
import { Sprint } from './sprint';
import { TimelineDay, FeatureRow, UserStoryBar, TimelineConfig } from './feature-timeline';

describe('Interfaces', () => {
  
  describe('WorkItem', () => {
    it('should create a valid WorkItem', () => {
      const workItem: WorkItem = {
        id: 1,
        fields: {
          'System.Id': 1,
          'System.Title': 'Test Work Item',
          'System.WorkItemType': 'Feature',
          'System.State': 'New'
        },
        url: 'test-url'
      };

      expect(workItem.id).toBe(1);
      expect(workItem.fields['System.Title']).toBe('Test Work Item');
      expect(workItem.url).toBe('test-url');
    });
  });

  describe('Sprint', () => {
    it('should create a valid Sprint', () => {
      const sprint: Sprint = {
        id: 'sprint-1',
        name: 'Sprint 1',
        path: 'Project\\Sprint 1',
        url: 'sprint-url',
        attributes: {
          startDate: '2024-01-01T00:00:00Z',
          finishDate: '2024-01-15T00:00:00Z',
          timeFrame: 'current'
        }
      };

      expect(sprint.id).toBe('sprint-1');
      expect(sprint.name).toBe('Sprint 1');
      expect(sprint.attributes.timeFrame).toBe('current');
    });
  });

  describe('TimelineDay', () => {
    it('should create a valid TimelineDay', () => {
      const timelineDay: TimelineDay = {
        date: new Date('2024-01-15'),
        dayName: 'Lun',
        dayNumber: '15',
        isToday: true
      };

      expect(timelineDay.date).toBeInstanceOf(Date);
      expect(timelineDay.dayName).toBe('Lun');
      expect(timelineDay.isToday).toBe(true);
    });
  });

  describe('UserStoryBar', () => {
    it('should create a valid UserStoryBar', () => {
      const userStory: WorkItem = {
        id: 2,
        fields: {
          'System.Id': 2,
          'System.Title': 'Test User Story',
          'System.WorkItemType': 'User Story',
          'System.State': 'Active'
        },
        url: 'us-url'
      };

      const userStoryBar: UserStoryBar = {
        userStory,
        leftPosition: 100,
        width: 200,
        line: 1
      };

      expect(userStoryBar.userStory).toBe(userStory);
      expect(userStoryBar.leftPosition).toBe(100);
      expect(userStoryBar.width).toBe(200);
      expect(userStoryBar.line).toBe(1);
    });
  });

  describe('FeatureRow', () => {
    it('should create a valid FeatureRow', () => {
      const feature: WorkItem = {
        id: 1,
        fields: {
          'System.Id': 1,
          'System.Title': 'Test Feature',
          'System.WorkItemType': 'Feature',
          'System.State': 'New'
        },
        url: 'feature-url'
      };

      const featureRow: FeatureRow = {
        feature,
        color: '#0078d4',
        userStories: [],
        isCollapsed: false
      };

      expect(featureRow.feature).toBe(feature);
      expect(featureRow.color).toBe('#0078d4');
      expect(featureRow.userStories).toEqual([]);
      expect(featureRow.isCollapsed).toBe(false);
    });
  });

  describe('TimelineConfig', () => {
    it('should create a valid TimelineConfig', () => {
      const config: TimelineConfig = {
        dayWidth: 120,
        rowHeight: 60,
        featureColumnWidth: 300,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        totalDays: 31
      };

      expect(config.dayWidth).toBe(120);
      expect(config.rowHeight).toBe(60);
      expect(config.featureColumnWidth).toBe(300);
      expect(config.startDate).toBeInstanceOf(Date);
      expect(config.endDate).toBeInstanceOf(Date);
      expect(config.totalDays).toBe(31);
    });
  });
});
