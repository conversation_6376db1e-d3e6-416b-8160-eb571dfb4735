# Manuel de mise à jour

Ce manuel décrit la procédure standard pour mettre à jour l'application Planify lorsqu'une nouvelle version est disponible. Il est destiné à un administrateur ou un opérateur chargé du déploiement.

## 1. Principe général

La mise à jour de l'application Planify est gérée par le déploiement de nouvelles images Docker, qui sont construites et versionnées automatiquement par le pipeline d'intégration continue. Le processus de mise à jour consiste simplement à indiquer au serveur quelle nouvelle version de l'image il doit utiliser.

## 2. Procédure de mise à jour standard

Lorsqu'une nouvelle version est validée et prête à être déployée (par exemple, la version `25.8.2-main`), la procédure est la suivante :

**1. Mettre à jour le tag de version :**

Éditez le fichier de configuration de l'environnement (par exemple, `.env`) utilisé par Docker Compose et modifiez la variable `PLANIFY_TAG` pour qu'elle corresponde à la nouvelle version à déployer.

*Avant la mise à jour :*

```
PLANIFY_TAG=25.8.1-main
```

*Après la mise à jour :*

```
PLANIFY_TAG=25.8.2-main
```

**2. Appliquer la mise à jour :**

À la racine du projet où se trouve le fichier `docker-compose.yml`, exécutez la commande suivante :

```bash
docker-compose down && docker-compose up -d
```

* **`docker-compose down`** : Arrête et supprime les anciens conteneurs.
* **`docker-compose up -d`** : Télécharge les nouvelles images depuis le registre Docker (en se basant sur le nouveau `PLANIFY_TAG`) et redémarre l'application en arrière-plan.

L'application est maintenant à jour et accessible avec la nouvelle version.

## 3\. Procédure de Rollback (Retour en arrière)

En cas de problème avec la nouvelle version, la procédure de retour en arrière est identique et tout aussi simple :

1.  **Rétablir l'ancien tag de version** dans le fichier d'environnement.
    ```
    PLANIFY_TAG=25.8.1-main
    ```
2.  **Relancer la commande** pour appliquer le changement :
    ```bash
    docker-compose down && docker-compose up -d
    ```

Docker Compose arrêtera la version problématique et redémarrera les conteneurs avec la version stable précédente, qui est toujours disponible dans le registre.