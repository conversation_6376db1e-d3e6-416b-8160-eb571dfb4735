import { TestBed, ComponentFixture } from '@angular/core/testing';

import { FeatureTimelineCalendarComponent } from './feature-timeline-calendar.component';
import { MOCK_SPRINTS, MOCK_FEATURE_WORK_ITEMS } from '../../testing/test-data';

/**
 * Tests unitaires pour FeatureTimelineCalendarComponent
 *
 * COUVERTURE MÉTIER :
 * - Construction de la timeline de planification
 * - Regroupement hiérarchique des features et user stories
 * - Génération des jours ouvrables pour la planification
 * - Logique de positionnement des éléments sur la timeline
 *
 * JUSTIFICATION :
 * Ces tests couvrent la logique métier complexe de planification :
 * - Construction de la timeline avec les bonnes règles métier (jours ouvrables)
 * - Regroupement des user stories sous leurs features parentes
 * - Calculs de positionnement pour l'affichage Gantt
 */

describe('FeatureTimelineCalendarComponent', () => {
  let component: FeatureTimelineCalendarComponent;
  let fixture: ComponentFixture<FeatureTimelineCalendarComponent>;



  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FeatureTimelineCalendarComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(FeatureTimelineCalendarComponent);
    component = fixture.componentInstance;
  });

  /**
   * PERTINENCE : Validation que le composant calendrier se construit
   * correctement avec les données de planification
   */
  it('devrait construire la timeline avec les données du sprint', () => {
    component.sprint = MOCK_SPRINTS[0];
    component.workItems = MOCK_FEATURE_WORK_ITEMS;

    component.ngOnChanges({
      sprint: {
        currentValue: MOCK_SPRINTS[0],
        previousValue: null,
        firstChange: true,
        isFirstChange: () => true
      },
      workItems: {
        currentValue: MOCK_FEATURE_WORK_ITEMS,
        previousValue: [],
        firstChange: false,
        isFirstChange: () => false
      }
    });

    // Vérification que la timeline est construite
    expect(component['timelineDays'].length).toBeGreaterThan(0);
    expect(component['featureRows'].length).toBe(1);
  });

  /**
   * PERTINENCE : Logique métier de regroupement des user stories
   * sous leurs features parentes pour l'affichage hiérarchique
   */
  it('devrait regrouper les user stories sous leurs features parentes', () => {
    component.sprint = MOCK_SPRINTS[0];
    component.workItems = MOCK_FEATURE_WORK_ITEMS;
    component['buildTimeline']();

    const featureRow = component['featureRows'][0];
    expect(featureRow.feature.fields['System.WorkItemType']).toBe('Feature');
    expect(featureRow.userStories.length).toBe(1);
    expect(featureRow.userStories[0].userStory.fields['System.Parent']).toBe(1);
  });

  /**
   * PERTINENCE : Génération des jours ouvrables uniquement (lundi-vendredi)
   * pour la planification, logique métier importante
   */
  it('devrait générer uniquement les jours ouvrables pour la planification', () => {
    component.sprint = MOCK_SPRINTS[0];
    component.workItems = MOCK_FEATURE_WORK_ITEMS;
    component['buildTimeline']();

    const weekendDays = component['timelineDays'].filter(day => {
      const dayOfWeek = day.date.getDay();
      return dayOfWeek === 0 || dayOfWeek === 6; // Dimanche ou Samedi
    });

    expect(weekendDays.length).toBe(0);
  });
});
