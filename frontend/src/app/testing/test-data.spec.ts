import { MOCK_SPRINTS, MOCK_WORK_ITEMS, MOCK_FEATURE_WORK_ITEMS } from './test-data';

describe('Test Data', () => {
  
  describe('MOCK_SPRINTS', () => {
    it('should contain valid sprint data', () => {
      expect(MOCK_SPRINTS).toBeDefined();
      expect(MOCK_SPRINTS.length).toBeGreaterThan(0);
      
      const sprint = MOCK_SPRINTS[0];
      expect(sprint.id).toBeDefined();
      expect(sprint.name).toBeDefined();
      expect(sprint.path).toBeDefined();
      expect(sprint.attributes).toBeDefined();
      expect(sprint.attributes.startDate).toBeDefined();
      expect(sprint.attributes.finishDate).toBeDefined();
      expect(sprint.attributes.timeFrame).toBeDefined();
    });

    it('should have different timeFrame values', () => {
      const timeFrames = MOCK_SPRINTS.map(sprint => sprint.attributes.timeFrame);
      const uniqueTimeFrames = [...new Set(timeFrames)];
      
      expect(uniqueTimeFrames.length).toBeGreaterThan(1);
      expect(uniqueTimeFrames).toContain('current');
    });
  });

  describe('MOCK_WORK_ITEMS', () => {
    it('should contain valid work item data', () => {
      expect(MOCK_WORK_ITEMS).toBeDefined();
      expect(MOCK_WORK_ITEMS.length).toBeGreaterThan(0);
      
      const workItem = MOCK_WORK_ITEMS[0];
      expect(workItem.id).toBeDefined();
      expect(workItem.fields).toBeDefined();
      expect(workItem.fields['System.Id']).toBeDefined();
      expect(workItem.fields['System.Title']).toBeDefined();
      expect(workItem.fields['System.WorkItemType']).toBeDefined();
      expect(workItem.url).toBeDefined();
    });

    it('should contain different work item types', () => {
      const workItemTypes = MOCK_WORK_ITEMS.map(item => item.fields['System.WorkItemType']);
      const uniqueTypes = [...new Set(workItemTypes)];
      
      expect(uniqueTypes).toContain('Feature');
      expect(uniqueTypes).toContain('User Story');
    });

    it('should have user stories with parent references', () => {
      const userStories = MOCK_WORK_ITEMS.filter(item => 
        item.fields['System.WorkItemType'] === 'User Story'
      );
      
      expect(userStories.length).toBeGreaterThan(0);
      
      const userStoryWithParent = userStories.find(us => 
        us.fields['System.Parent'] !== undefined
      );
      
      expect(userStoryWithParent).toBeDefined();
    });
  });

  describe('MOCK_FEATURE_WORK_ITEMS', () => {
    it('should contain features and related user stories', () => {
      expect(MOCK_FEATURE_WORK_ITEMS).toBeDefined();
      expect(MOCK_FEATURE_WORK_ITEMS.length).toBeGreaterThan(0);
      
      const features = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['System.WorkItemType'] === 'Feature'
      );
      const userStories = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['System.WorkItemType'] === 'User Story'
      );
      
      expect(features.length).toBeGreaterThan(0);
      expect(userStories.length).toBeGreaterThan(0);
    });

    it('should have consistent parent-child relationships', () => {
      const features = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['System.WorkItemType'] === 'Feature'
      );
      const userStories = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['System.WorkItemType'] === 'User Story'
      );
      
      const featureIds = features.map(f => f.id);
      
      userStories.forEach(us => {
        const parentId = us.fields['System.Parent'];
        if (parentId) {
          expect(featureIds).toContain(parentId);
        }
      });
    });

    it('should have work items with scheduling dates', () => {
      const itemsWithDates = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['Microsoft.VSTS.Scheduling.StartDate'] || 
        item.fields['Microsoft.VSTS.Scheduling.FinishDate']
      );
      
      expect(itemsWithDates.length).toBeGreaterThan(0);
    });

    it('should have work items with assigned users', () => {
      const assignedItems = MOCK_FEATURE_WORK_ITEMS.filter(item => 
        item.fields['System.AssignedTo']
      );
      
      expect(assignedItems.length).toBeGreaterThan(0);
      
      const assignedItem = assignedItems[0];
      expect(assignedItem.fields['System.AssignedTo'].displayName).toBeDefined();
    });

    it('should have work items with different states', () => {
      const states = MOCK_FEATURE_WORK_ITEMS.map(item => item.fields['System.State']);
      const uniqueStates = [...new Set(states)];
      
      expect(uniqueStates.length).toBeGreaterThan(1);
      expect(uniqueStates).toContain('New');
    });
  });
});
