# Manuel de Déploiement - Planify

## Vue d'ensemble

Planify est une application web de planification multi-sprints pour Azure DevOps Server, composée d'un frontend Angular et d'un backend Python Flask. Ce manuel détaille les procédures complètes de déploiement en production.

## Architecture de déploiement

- **Frontend** : Application Angular servie par Nginx (port 4200)
- **Backend** : API Flask avec Gunicorn (port 5000)
- **Conteneurisation** : Docker et Docker Compose
- **Images de base** : 
  - Frontend : `itesoft/external/nginx1:25.6.4`
  - Backend : `itesoft/external/python3_11:25.7.1`

## Prérequis

### Environnement de production
- Docker Engine 20.10+
- Docker Compose 2.0+
- Accès au registre Docker privé
- Accès à Azure DevOps Server

### Permissions Azure DevOps requises
- Work Items (Read)
- Analytics (Read)
- Personal Access Token (PAT) valide

## Configuration des variables d'environnement

### Variables backend (env/backend.env)

C<PERSON>er le fichier `env/backend.env` basé sur `backend/.env.example` :

```env
# Configuration Azure DevOps
AZURE_DEVOPS_SERVER_URL=https://your-azuredevops-server.com
AZURE_DEVOPS_ORG=YourOrganization
AZURE_DEVOPS_PROJECT=YourProject
AZURE_DEVOPS_TEAM=YourTeam
AZURE_DEVOPS_PAT=your-personal-access-token

# Filtrage des sprints (optionnel)
SPRINT_PATH_PREFIX=YourProject\Sprint

# Configuration SSL (développement uniquement)
DISABLE_SSL_WARNINGS=false
```

### Variables frontend (env/web-app.env)

Créer le fichier `env/web-app.env` :

```env
# URL de l'API backend
API_BASE_URL=http://planify-backend:5000/api

# Base href pour l'application
BASE_HREF=/
```

## Build et publication des images Docker

### Build automatique via GitLab CI

Le pipeline GitLab CI (`.gitlab-ci.yml`) gère automatiquement :

1. **Build frontend** :
   ```bash
   cd frontend
   npm install --no-audit
   npm run build
   ```

2. **Build images Docker** :
   - Image frontend : `itesoft/slfi/qa/planify-web-app:${VERSION}`
   - Image backend : `itesoft/slfi/qa/planify-backend:${VERSION}`

3. **Publication** vers le registre Docker privé

### Build manuel

#### Frontend
```bash
# Build de l'application Angular
cd frontend
npm install --no-audit
npm run build

# Préparation de l'image Docker
mkdir -p target/root/usr/share/nginx/html
cp docker/Dockerfile target/
cp -r docker/root/* target/root/
cp -r dist/frontend/browser/* target/root/usr/share/nginx/html/

# Build de l'image
docker build -t planify-web-app:latest \
  --build-arg="VERSION=manual" \
  --build-arg CI_COMMIT_SHA=$(git rev-parse HEAD) \
  --build-arg CI_JOB_STARTED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  target/
```

#### Backend
```bash
# Préparation de l'image Docker
cd backend
mkdir -p target
cp requirements.txt target/
cp -r src target/
cp docker/Dockerfile target/

# Build de l'image
docker build -t planify-backend:latest \
  --build-arg="VERSION=manual" \
  --build-arg CI_COMMIT_SHA=$(git rev-parse HEAD) \
  --build-arg CI_JOB_STARTED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
  target/
```

## Déploiement avec Docker Compose

### Configuration Docker Compose

Le fichier `docker-compose.yml` définit les services :

```yaml
services:
  planify-web-app:
    image: ${REGISTRY:-}itesoft/slfi/qa/planify-web-app:${PLANIFY_TAG}
    container_name: planify-web-app
    env_file:
      - env/web-app.env
    ports:
      - 4200:80
    networks:
      - planify

  planify-backend:
    image: ${REGISTRY:-}itesoft/slfi/qa/planify-backend:${PLANIFY_TAG}
    container_name: planify-backend
    env_file:
      - env/backend.env
    ports:
      - 5000:5000
    networks:
      - planify

networks:
  planify:
    driver: bridge
```

### Variables d'environnement Docker Compose

Créer un fichier `.env` à la racine :

```env
REGISTRY=your-docker-registry.com/
PLANIFY_TAG=25.8.1-latest
```

### Déploiement

```bash
# Démarrage des services
docker-compose up -d

# Vérification du statut
docker-compose ps

# Consultation des logs
docker-compose logs -f planify-web-app
docker-compose logs -f planify-backend
```

## Configuration Nginx

### Configuration automatique

Le frontend utilise un script de démarrage (`frontend/docker/root/start.d/05_update-config-settings.sh`) qui configure automatiquement :

- `apiBaseUrl` depuis la variable `API_BASE_URL`
- `baseHref` depuis la variable `BASE_HREF`

### Configuration manuelle

Le fichier `frontend/src/settings/environment.json` est mis à jour au démarrage du conteneur :

```json
{
  "apiBaseUrl": "http://planify-backend:5000/api",
  "baseHref": "/"
}
```

### Configuration Nginx personnalisée

Fichier de configuration : `frontend/docker/root/etc/nginx/conf.d/location.conf`

```nginx
location / {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /index.html;
    index index.html;
}
```

## Vérifications post-déploiement

### Health checks automatiques

#### Backend
Le conteneur backend inclut un health check automatique :

```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1
```

#### Vérification manuelle

```bash
# Test de l'API backend
curl http://localhost:5000/api/health

# Réponse attendue :
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z"
}

# Test de l'application frontend
curl http://localhost:4200/

# Test de la connectivité Azure DevOps
curl http://localhost:5000/api/sprints
```

### Vérifications fonctionnelles

1. **Interface utilisateur** :
   - Accès à `http://localhost:4200`
   - Chargement de la page d'accueil
   - Sélection d'un sprint dans le dropdown

2. **API Backend** :
   - Endpoint `/api/health` retourne status "healthy"
   - Endpoint `/api/sprints` retourne la liste des sprints
   - Logs sans erreurs de connexion Azure DevOps

3. **Intégration Azure DevOps** :
   - Authentification PAT fonctionnelle
   - Récupération des work items
   - Affichage des features et user stories

### Surveillance et logs

```bash
# Surveillance en temps réel
docker-compose logs -f

# Logs spécifiques par service
docker-compose logs planify-backend
docker-compose logs planify-web-app

# Vérification des conteneurs
docker-compose ps
docker stats
```

## Gestion des versions

### Versioning automatique

Le versioning est géré par :
- Fichier `planify.properties` : `version.prefix=25.8.1`
- Scripts de build dans `build-scripts/`
- Pipeline GitLab CI

### Mise à jour de version

```bash
# Modification du fichier planify.properties
echo "version.prefix=25.8.2" > planify.properties

# Rebuild et redéploiement via GitLab CI
git add planify.properties
git commit -m "Bump version to 25.8.2"
git push
```

## Dépannage

### Problèmes courants

1. **Erreur de connexion Azure DevOps** :
   - Vérifier les variables d'environnement backend
   - Valider le PAT Azure DevOps
   - Contrôler la connectivité réseau

2. **Frontend inaccessible** :
   - Vérifier les logs Nginx
   - Contrôler la configuration `environment.json`
   - Valider les variables `API_BASE_URL`

3. **Problèmes de build** :
   - Vérifier les dépendances npm (`package.json`)
   - Contrôler les dépendances Python (`requirements.txt`)
   - Valider les images de base Docker

### Commandes de diagnostic

```bash
# Inspection des conteneurs
docker inspect planify-web-app
docker inspect planify-backend

# Accès aux conteneurs
docker exec -it planify-web-app sh
docker exec -it planify-backend bash

# Nettoyage complet
docker-compose down -v
docker system prune -f
```

## Sécurité

### Recommandations de production

1. **Variables sensibles** :
   - Ne jamais versionner les fichiers `.env`
   - Utiliser des secrets Docker ou Kubernetes
   - Rotation régulière des PAT Azure DevOps

2. **Configuration SSL** :
   - Désactiver `DISABLE_SSL_WARNINGS` en production
   - Utiliser HTTPS pour tous les endpoints
   - Certificats SSL valides

3. **Réseau** :
   - Isoler les conteneurs dans un réseau privé
   - Exposer uniquement les ports nécessaires
   - Utiliser un reverse proxy (nginx, traefik)

## Sauvegarde et restauration

### Données à sauvegarder

- Fichiers de configuration (`env/`)
- Images Docker buildées
- Logs d'application

### Procédure de restauration

```bash
# Sauvegarde des images
docker save planify-web-app:latest > planify-web-app.tar
docker save planify-backend:latest > planify-backend.tar

# Restauration
docker load < planify-web-app.tar
docker load < planify-backend.tar

# Redémarrage
docker-compose up -d
```
