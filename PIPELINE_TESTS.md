# Tests dans la Pipeline GitLab CI - Planify

## Vue d'ensemble

La pipeline GitLab CI de Planify intègre une phase de tests complète qui valide la qualité du code avant le déploiement. Cette phase respecte les bonnes pratiques DevOps et assure la non-régression du code.

## Architecture des tests

### Stage `test`

Le stage `test` contient 6 jobs parallèles qui valident différents aspects de l'application :

1. **test-frontend-unit** : Tests unitaires Angular avec Karma/Jasmine
2. **test-frontend-coverage** : Validation de la couverture de code
3. **test-backend-unit** : Tests unitaires Python avec pytest
4. **test-backend-api** : Tests d'intégration API Flask
5. **test-lint-frontend** : Validation du code avec ESLint
6. **test-build-validation** : Validation du build de production

## Jobs de test détaillés

### test-frontend-unit

**Objectif** : Exécuter tous les tests unitaires Angular et générer les rapports

```yaml
test-frontend-unit:
  stage: test
  extends: .default-node-builder
  script:
    - cd $WEB_APP_PATH
    - npm ci --no-audit
    - npm run test:ci
```

**Fonctionnalités** :
- Exécution des tests avec ChromeHeadless
- Génération de rapports JUnit pour GitLab
- Génération de rapports de couverture Cobertura
- Extraction automatique du pourcentage de couverture

**Artefacts générés** :
- `coverage/` : Rapports HTML et XML de couverture
- `junit.xml` : Rapport de tests pour GitLab
- `cobertura-coverage.xml` : Rapport de couverture pour GitLab

### test-frontend-coverage

**Objectif** : Valider que les seuils de couverture sont respectés

```yaml
test-frontend-coverage:
  script:
    - npm run test:coverage
    - npm run coverage:check
```

**Seuils configurés** :
- **Statements** : 80%
- **Branches** : 75%
- **Functions** : 80%
- **Lines** : 80%

**Comportement** : Le job échoue si les seuils ne sont pas atteints, bloquant la pipeline.

### test-backend-unit

**Objectif** : Exécuter les tests unitaires Python avec pytest

```yaml
test-backend-unit:
  script:
    - pip install pytest pytest-cov pytest-flask
    - python -m pytest tests/ --cov=src --cov-report=xml
```

**Fonctionnalités** :
- Tests unitaires de l'API Flask
- Tests des endpoints avec mocks
- Validation de la structure des données
- Génération de rapports de couverture

**Configuration** : `allow_failure: true` temporairement pour éviter de bloquer la pipeline

### test-backend-api

**Objectif** : Tests d'intégration de l'API en conditions réelles

```yaml
test-backend-api:
  script:
    - python src/app.py &
    - sleep 5
    - curl -f http://localhost:5000/api/health
    - curl -f http://localhost:5000/api/sprints
```

**Tests effectués** :
- Démarrage de l'application Flask
- Test de l'endpoint `/api/health`
- Test de l'endpoint `/api/sprints`
- Validation des codes de réponse HTTP

### test-lint-frontend

**Objectif** : Validation de la qualité du code TypeScript

```yaml
test-lint-frontend:
  script:
    - npx ng lint
```

**Validations** :
- Respect des conventions de codage Angular
- Détection des erreurs TypeScript
- Validation des bonnes pratiques

**Configuration** : `allow_failure: true` pour ne pas bloquer sur les warnings

### test-build-validation

**Objectif** : Validation du build de production

```yaml
test-build-validation:
  script:
    - npm run build
    - ls -la dist/frontend/browser/
    - du -sh dist/frontend/browser/
```

**Validations** :
- Build sans erreur en mode production
- Génération des artefacts attendus
- Vérification de la taille du bundle

## Configuration des rapports

### Rapports GitLab CI

La pipeline génère des rapports intégrés à GitLab :

```yaml
reports:
  junit: $WEB_APP_PATH/coverage/planify/junit.xml
  coverage_report:
    coverage_format: cobertura
    path: $WEB_APP_PATH/coverage/planify/cobertura-coverage.xml
```

**Avantages** :
- Visualisation des tests dans l'interface GitLab
- Historique de la couverture de code
- Détection automatique des régressions
- Intégration avec les merge requests

### Extraction de la couverture

```yaml
coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
```

Cette regex extrait automatiquement le pourcentage de couverture des logs pour l'afficher dans GitLab.

## Artefacts et conservation

### Artefacts de test

Tous les rapports sont conservés comme artefacts :

```yaml
artifacts:
  name: frontend-test-results
  when: always
  expire_in: 1 week
  paths:
    - $WEB_APP_PATH/coverage/
```

**Durée de conservation** : 1 semaine pour les rapports de test, 1 jour pour la validation de build.

### Disponibilité

- **when: always** : Artefacts générés même en cas d'échec
- **when: on_success** : Artefacts générés seulement en cas de succès

## Optimisations de performance

### Cache des dépendances

```yaml
script:
  - npm ci --no-audit
```

Utilisation de `npm ci` au lieu de `npm install` pour :
- Installation plus rapide et déterministe
- Respect exact du package-lock.json
- Pas d'audit de sécurité (gain de temps)

### Parallélisation

Les 6 jobs de test s'exécutent en parallèle pour réduire le temps total de la pipeline.

### Images optimisées

- **node20-builder** : Image pré-configurée avec Node.js 20
- **python:3.11-slim** : Image Python légère

## Intégration avec le développement

### Merge Requests

Les tests s'exécutent automatiquement sur chaque merge request :
- Validation avant fusion
- Rapports de couverture comparatifs
- Blocage automatique si tests échouent

### Branches

Configuration avec `!reference [ .building_rules, rules ]` pour s'exécuter sur :
- Branches de développement
- Tags de release
- Merge requests

## Monitoring et alertes

### Métriques surveillées

1. **Taux de réussite des tests** : Doit être > 95%
2. **Couverture de code** : Doit respecter les seuils configurés
3. **Temps d'exécution** : Surveillance des régressions de performance
4. **Taille du bundle** : Alerte si augmentation significative

### Notifications

En cas d'échec :
- Notification par email aux développeurs
- Blocage automatique de la merge request
- Rapport détaillé dans GitLab

## Maintenance et évolution

### Mise à jour des seuils

Les seuils de couverture peuvent être ajustés dans :
- `frontend/package.json` : Script `coverage:check`
- `frontend/karma.conf.js` : Configuration des watermarks
- `backend/pytest.ini` : Configuration pytest

### Ajout de nouveaux tests

Pour ajouter de nouveaux types de tests :
1. Créer un nouveau job dans `.gitlab-ci.yml`
2. Configurer les artefacts et rapports
3. Ajouter les dépendances nécessaires
4. Documenter dans ce fichier

### Tests de régression

Les tests de régression sont intégrés dans les jobs existants :
- Tests unitaires : Détection automatique des régressions
- Tests d'intégration : Validation des APIs
- Tests de build : Validation de la compatibilité

## Bonnes pratiques

### Écriture de tests

1. **Tests unitaires** : Un test par fonctionnalité
2. **Tests d'intégration** : Validation des workflows complets
3. **Mocks appropriés** : Isolation des dépendances externes
4. **Assertions claires** : Messages d'erreur explicites

### Maintenance

1. **Révision régulière** : Mise à jour des dépendances de test
2. **Nettoyage** : Suppression des tests obsolètes
3. **Performance** : Optimisation des tests lents
4. **Documentation** : Mise à jour de cette documentation

## Dépannage

### Échecs fréquents

1. **Tests flaky** : Tests instables à corriger
2. **Timeouts** : Augmenter les délais si nécessaire
3. **Dépendances** : Vérifier les versions dans package.json
4. **Environnement** : Valider la configuration CI

### Commandes de debug

```bash
# Exécution locale des tests
cd frontend
npm test
npm run test:coverage

# Tests backend
cd backend
python -m pytest tests/ -v

# Validation du build
npm run build
```

Cette architecture de tests garantit la qualité et la stabilité du code Planify à chaque déploiement.
